'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Activity, BreathingState } from '@/types';

interface BreathOrbProps {
  activity: Activity;
  onComplete: (coinsEarned: number) => void;
  timeSlot: number;
}

export default function BreathOrb({ activity, onComplete, timeSlot }: BreathOrbProps) {
  const [breathingState, setBreathingState] = useState<BreathingState>({
    phase: 'inhale',
    cycleCount: 0,
    isActive: false,
    duration: timeSlot * 60, // Convert minutes to seconds
  });

  const [timeRemaining, setTimeRemaining] = useState(timeSlot * 60);
  const [phaseTimer, setPhaseTimer] = useState(0);
  const [breathParticles, setBreathParticles] = useState<Array<{id: number, x: number, y: number, delay: number}>>([]);

  // Breathing pattern: 4-4-4-4 (inhale-hold-exhale-pause)
  const phaseDurations = {
    inhale: 4,
    hold: 4,
    exhale: 4,
    pause: 4,
  };

  const phaseInstructions = {
    inhale: 'Breathe In',
    hold: 'Hold',
    exhale: 'Breathe Out',
    pause: 'Pause',
  };

  const phaseColors = {
    inhale: 'from-blue-400 to-cyan-400',
    hold: 'from-purple-400 to-blue-400',
    exhale: 'from-green-400 to-blue-400',
    pause: 'from-gray-400 to-gray-500',
  };

  // Initialize breath particles
  useEffect(() => {
    const particles = Array.from({ length: 15 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 8
    }));
    setBreathParticles(particles);
  }, []);

  // Start breathing session
  const startBreathing = () => {
    setBreathingState(prev => ({ ...prev, isActive: true }));
  };

  // Complete the session
  const completeSession = () => {
    setBreathingState(prev => ({ ...prev, isActive: false }));
    const coinsEarned = Math.max(8, Math.floor(breathingState.cycleCount / 2) * 3);
    onComplete(coinsEarned);
  };

  // Main timer effect
  useEffect(() => {
    if (breathingState.isActive && timeRemaining > 0) {
      const timer = setTimeout(() => {
        setTimeRemaining(prev => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (breathingState.isActive && timeRemaining <= 0) {
      completeSession();
    }
  }, [breathingState.isActive, timeRemaining]);

  // Phase timer effect
  useEffect(() => {
    if (breathingState.isActive) {
      const timer = setTimeout(() => {
        setPhaseTimer(prev => prev + 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [breathingState.isActive, phaseTimer]);

  // Phase transition effect
  useEffect(() => {
    if (breathingState.isActive) {
      const currentPhaseDuration = phaseDurations[breathingState.phase];
      
      if (phaseTimer >= currentPhaseDuration) {
        setPhaseTimer(0);
        
        setBreathingState(prev => {
          let nextPhase: BreathingState['phase'];
          let newCycleCount = prev.cycleCount;
          
          switch (prev.phase) {
            case 'inhale':
              nextPhase = 'hold';
              break;
            case 'hold':
              nextPhase = 'exhale';
              break;
            case 'exhale':
              nextPhase = 'pause';
              break;
            case 'pause':
              nextPhase = 'inhale';
              newCycleCount += 1;
              break;
          }
          
          return {
            ...prev,
            phase: nextPhase,
            cycleCount: newCycleCount,
          };
        });
      }
    }
  }, [phaseTimer, breathingState.phase, breathingState.isActive]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Calculate orb scale based on phase and timer
  const getOrbScale = () => {
    const progress = phaseTimer / phaseDurations[breathingState.phase];
    
    switch (breathingState.phase) {
      case 'inhale':
        return 0.5 + (progress * 0.5); // Scale from 0.5 to 1.0
      case 'hold':
        return 1.0; // Stay at full size
      case 'exhale':
        return 1.0 - (progress * 0.5); // Scale from 1.0 to 0.5
      case 'pause':
        return 0.5; // Stay at small size
      default:
        return 0.5;
    }
  };

  const orbScale = getOrbScale();

  return (
    <div className="text-center relative overflow-hidden">
      {/* Ambient Background Particles */}
      <div className="absolute inset-0 pointer-events-none">
        {breathParticles.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute w-2 h-2 bg-white/20 rounded-full"
            style={{ left: `${particle.x}%`, top: `${particle.y}%` }}
            animate={{
              y: [-20, -40, -20],
              opacity: [0.2, 0.6, 0.2],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              delay: particle.delay,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      {/* Session Stats */}
      <div className="flex justify-between items-center mb-6 text-white relative z-10">
        <motion.div
          className="glass px-4 py-2 rounded-lg"
          animate={{ scale: breathingState.cycleCount > 0 ? [1, 1.1, 1] : 1 }}
          transition={{ duration: 0.5 }}
        >
          Cycles: <span className="font-bold text-cyan-400">{breathingState.cycleCount}</span>
        </motion.div>
        <motion.div
          className="glass px-4 py-2 rounded-lg"
          animate={{
            color: timeRemaining < 30 ? ["#ffffff", "#06b6d4", "#ffffff"] : "#ffffff"
          }}
          transition={{ duration: 1, repeat: timeRemaining < 30 ? Infinity : 0 }}
        >
          Time: {formatTime(timeRemaining)}
        </motion.div>
      </div>

      {/* Breathing Orb */}
      <div className="relative mb-8">
        <div className="flex items-center justify-center h-80 relative">
          {/* Outer glow rings */}
          <motion.div
            className="absolute w-80 h-80 rounded-full border border-white/10"
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.3, 0.1, 0.3]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute w-96 h-96 rounded-full border border-white/5"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.2, 0.05, 0.2]
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          />

          {/* Main breathing orb */}
          <motion.div
            className={`
              w-64 h-64 rounded-full bg-gradient-to-br ${phaseColors[breathingState.phase]}
              shadow-2xl relative overflow-hidden
            `}
            animate={{
              scale: orbScale,
              boxShadow: [
                `0 0 40px rgba(59, 130, 246, 0.3)`,
                `0 0 80px rgba(59, 130, 246, 0.6)`,
                `0 0 40px rgba(59, 130, 246, 0.3)`
              ]
            }}
            transition={{
              scale: { duration: 1, ease: "easeInOut" },
              boxShadow: { duration: 2, repeat: Infinity }
            }}
          >
            {/* Shimmer effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              animate={{ x: ["-100%", "100%"] }}
              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
            />
          </motion.div>

          {/* Inner orb for extra effect */}
          <motion.div
            className={`
              absolute w-48 h-48 rounded-full bg-gradient-to-br ${phaseColors[breathingState.phase]}
              opacity-60
            `}
            animate={{
              scale: orbScale * 0.8,
            }}
            transition={{ duration: 1, ease: "easeInOut" }}
          />

          {/* Center dot */}
          <motion.div
            className="absolute w-4 h-4 bg-white/80 rounded-full"
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.8, 0.4, 0.8]
            }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </div>
      </div>

      {/* Phase Instruction */}
      <motion.div
        className="text-white text-2xl font-semibold mb-4 relative z-10"
        key={breathingState.phase}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.span
          animate={{
            scale: breathingState.isActive ? [1, 1.1, 1] : 1,
            textShadow: [
              "0 0 10px rgba(255, 255, 255, 0.3)",
              "0 0 20px rgba(255, 255, 255, 0.6)",
              "0 0 10px rgba(255, 255, 255, 0.3)"
            ]
          }}
          transition={{
            scale: { duration: 1, repeat: Infinity },
            textShadow: { duration: 2, repeat: Infinity }
          }}
        >
          {breathingState.isActive ? phaseInstructions[breathingState.phase] : 'Ready to breathe?'}
        </motion.span>
      </motion.div>

      {/* Phase Progress */}
      <AnimatePresence>
        {breathingState.isActive && (
          <motion.div
            className="mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            <div className="w-32 h-2 bg-white/20 rounded-full mx-auto glass">
              <motion.div
                className="h-full bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full"
                animate={{
                  width: `${(phaseTimer / phaseDurations[breathingState.phase]) * 100}%`,
                  boxShadow: [
                    "0 0 10px rgba(6, 182, 212, 0.5)",
                    "0 0 20px rgba(6, 182, 212, 0.8)",
                    "0 0 10px rgba(6, 182, 212, 0.5)"
                  ]
                }}
                transition={{
                  width: { duration: 1 },
                  boxShadow: { duration: 1.5, repeat: Infinity }
                }}
              />
            </div>
            <motion.div
              className="text-white/60 text-sm mt-2"
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 1, repeat: Infinity }}
            >
              {phaseDurations[breathingState.phase] - phaseTimer}s
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Start/Status */}
      <AnimatePresence>
        {!breathingState.isActive && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ delay: 0.3 }}
          >
            <motion.button
              onClick={startBreathing}
              className="btn-magical text-white font-bold py-3 px-8 rounded-lg mb-4"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              Start Breathing
            </motion.button>

            <motion.div
              className="text-white/60 text-sm glass p-4 rounded-lg max-w-xs mx-auto"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              <p>Follow the breathing orb to calm your mind.</p>
              <p>Inhale as it grows, exhale as it shrinks.</p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {breathingState.isActive && (
          <motion.button
            onClick={completeSession}
            className="glass text-white font-medium py-2 px-6 rounded-lg hover-lift"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            whileHover={{ scale: 1.05, backgroundColor: "rgba(255, 255, 255, 0.3)" }}
            whileTap={{ scale: 0.95 }}
          >
            Finish Early
          </motion.button>
        )}
      </AnimatePresence>
    </div>
  );
}
