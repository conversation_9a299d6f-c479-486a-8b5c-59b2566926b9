'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import confetti from 'canvas-confetti';
import { Mood, TimeSlot } from '@/types';

// Floating particles component
const FloatingParticles = () => {
  const [particles, setParticles] = useState<Array<{id: number, x: number, y: number, delay: number}>>([]);

  useEffect(() => {
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 6
    }));
    setParticles(newParticles);
  }, []);

  return (
    <div className="floating-particles">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="particle"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
          }}
          animate={{
            y: [-20, -40, -20],
            x: [-10, 10, -10],
            opacity: [0.3, 0.8, 0.3],
            scale: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            delay: particle.delay,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );
};

export default function Home() {
  const router = useRouter();
  const [showMoodSelector, setShowMoodSelector] = useState(false);
  const [showTimeSelector, setShowTimeSelector] = useState(false);
  const [selectedMood, setSelectedMood] = useState<Mood | null>(null);
  const [selectedTime, setSelectedTime] = useState<TimeSlot | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
    // Celebration confetti on page load
    setTimeout(() => {
      confetti({
        particleCount: 50,
        spread: 70,
        origin: { y: 0.8 },
        colors: ['#667eea', '#764ba2', '#f093fb', '#f5576c']
      });
    }, 1000);
  }, []);

  const moods: { value: Mood; label: string; icon: string; color: string }[] = [
    { value: 'energetic', label: 'Energetic', icon: '⚡', color: 'bg-yellow-500' },
    { value: 'calm', label: 'Calm', icon: '🌊', color: 'bg-blue-500' },
    { value: 'focused', label: 'Focused', icon: '🎯', color: 'bg-green-500' },
    { value: 'creative', label: 'Creative', icon: '🎨', color: 'bg-purple-500' },
    { value: 'stressed', label: 'Stressed', icon: '😤', color: 'bg-red-500' },
    { value: 'bored', label: 'Bored', icon: '😴', color: 'bg-gray-500' },
  ];

  const timeSlots: { value: TimeSlot; label: string }[] = [
    { value: 1, label: '1 min' },
    { value: 3, label: '3 min' },
    { value: 5, label: '5 min' },
    { value: 10, label: '10+ min' },
  ];

  const handleShiftNow = () => {
    if (!showMoodSelector && !showTimeSelector) {
      setShowMoodSelector(true);
      // Small celebration
      confetti({
        particleCount: 30,
        spread: 50,
        origin: { y: 0.7 },
        colors: ['#667eea', '#764ba2']
      });
      return;
    }

    if (showMoodSelector && !selectedMood) {
      return;
    }

    if (!showTimeSelector) {
      setShowTimeSelector(true);
      // Another small celebration
      confetti({
        particleCount: 25,
        spread: 40,
        origin: { y: 0.7 },
        colors: ['#f093fb', '#f5576c']
      });
      return;
    }

    if (!selectedTime) {
      return;
    }

    // Big celebration before navigation
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 },
      colors: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe']
    });

    // Navigate to activity page
    const params = new URLSearchParams();
    if (selectedMood) params.set('mood', selectedMood);
    if (selectedTime) params.set('time', selectedTime.toString());

    setTimeout(() => {
      router.push(`/activity?${params.toString()}`);
    }, 500);
  };

  const resetSelection = () => {
    setShowMoodSelector(false);
    setShowTimeSelector(false);
    setSelectedMood(null);
    setSelectedTime(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex flex-col relative overflow-hidden">
      {/* Floating Particles Background */}
      <FloatingParticles />

      {/* Animated Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/50 via-purple-900/50 to-pink-900/50 animate-pulse" />

      {/* Header */}
      <motion.header
        className="p-6 flex justify-between items-center relative z-10"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        <motion.div
          className="flex items-center gap-3"
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <motion.div
            className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center glass"
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          >
            <Zap className="w-6 h-6 text-white" />
          </motion.div>
          <h1 className="text-2xl font-bold text-white">ImpulseShift</h1>
        </motion.div>

        <motion.div
          className="flex items-center gap-4 text-white/80"
          initial={{ x: 50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <motion.div
            className="flex items-center gap-2 glass px-3 py-1 rounded-full"
            whileHover={{ scale: 1.1, backgroundColor: "rgba(255, 255, 255, 0.2)" }}
          >
            <Trophy className="w-5 h-5 text-yellow-400" />
            <span className="text-sm font-semibold">1,247 coins</span>
          </motion.div>
          <motion.div
            className="flex items-center gap-2 glass px-3 py-1 rounded-full"
            whileHover={{ scale: 1.1, backgroundColor: "rgba(255, 255, 255, 0.2)" }}
          >
            <Sparkles className="w-5 h-5 text-pink-400" />
            <span className="text-sm font-semibold">5 day streak</span>
          </motion.div>
        </motion.div>
      </motion.header>

      {/* Main Content */}
      <main className="flex-1 flex flex-col items-center justify-center p-8 text-center relative z-10">
        <motion.div
          className="max-w-2xl mx-auto"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 1, delay: 0.3 }}
        >
          {/* Main Message */}
          <motion.div
            className="mb-12"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <motion.h2
              className="text-5xl font-bold text-white mb-4"
              animate={{
                textShadow: [
                  "0 0 20px rgba(102, 126, 234, 0.5)",
                  "0 0 40px rgba(102, 126, 234, 0.8)",
                  "0 0 20px rgba(102, 126, 234, 0.5)"
                ]
              }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              Let's Shift Your Impulse
            </motion.h2>
            <motion.p
              className="text-xl text-white/80 mb-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
            >
              Transform mindless scrolling into something amazing
            </motion.p>
            <motion.p
              className="text-sm text-white/60"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
            >
              You've avoided <motion.span
                className="font-semibold text-white"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >47 minutes</motion.span> of doomscrolling this week! 🎉
            </motion.p>
          </motion.div>

          {/* Mood Selection */}
          <AnimatePresence>
            {showMoodSelector && (
              <motion.div
                className="mb-8"
                initial={{ y: 50, opacity: 0, scale: 0.9 }}
                animate={{ y: 0, opacity: 1, scale: 1 }}
                exit={{ y: -50, opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5, type: "spring" }}
              >
                <motion.h3
                  className="text-2xl font-semibold text-white mb-6"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  How are you feeling?
                </motion.h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                  {moods.map((mood, index) => (
                    <motion.button
                      key={mood.value}
                      onClick={() => setSelectedMood(mood.value)}
                      className={`p-4 rounded-xl border-2 transition-all duration-200 glass ${
                        selectedMood === mood.value
                          ? 'border-white bg-white/30 scale-105 shadow-lg'
                          : 'border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50'
                      }`}
                      initial={{ y: 30, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.1 * index, type: "spring" }}
                      whileHover={{ scale: 1.05, y: -5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <motion.div
                        className="text-3xl mb-2"
                        animate={selectedMood === mood.value ? { scale: [1, 1.2, 1] } : {}}
                        transition={{ duration: 0.3 }}
                      >
                        {mood.icon}
                      </motion.div>
                      <div className="text-white font-medium">{mood.label}</div>
                    </motion.button>
                  ))}
                </div>
                <motion.button
                  onClick={() => setSelectedMood('focused')}
                  className="text-white/60 hover:text-white/80 text-sm underline"
                  whileHover={{ scale: 1.05 }}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  Skip - surprise me!
                </motion.button>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Time Selection */}
          <AnimatePresence>
            {showTimeSelector && (
              <motion.div
                className="mb-8"
                initial={{ y: 50, opacity: 0, scale: 0.9 }}
                animate={{ y: 0, opacity: 1, scale: 1 }}
                exit={{ y: -50, opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5, type: "spring" }}
              >
                <motion.h3
                  className="text-2xl font-semibold text-white mb-6"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  How much time do you have?
                </motion.h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  {timeSlots.map((slot, index) => (
                    <motion.button
                      key={slot.value}
                      onClick={() => setSelectedTime(slot.value)}
                      className={`p-4 rounded-xl border-2 transition-all duration-200 glass ${
                        selectedTime === slot.value
                          ? 'border-white bg-white/30 scale-105 shadow-lg'
                          : 'border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50'
                      }`}
                      initial={{ y: 30, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.1 * index, type: "spring" }}
                      whileHover={{ scale: 1.05, y: -5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <motion.div
                        animate={selectedTime === slot.value ? { rotate: 360 } : {}}
                        transition={{ duration: 0.5 }}
                      >
                        <Clock className="w-6 h-6 mx-auto mb-2 text-white" />
                      </motion.div>
                      <div className="text-white font-medium">{slot.label}</div>
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Action Button */}
          <motion.div
            className="mb-8"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.6, type: "spring" }}
          >
            <motion.button
              onClick={handleShiftNow}
              className="btn-magical text-white font-bold py-6 px-12 rounded-full text-2xl shadow-2xl flex items-center gap-3 mx-auto relative overflow-hidden"
              whileHover={{
                scale: 1.05,
                y: -5,
                boxShadow: "0 20px 40px rgba(102, 126, 234, 0.4)"
              }}
              whileTap={{ scale: 0.95 }}
              animate={{
                boxShadow: [
                  "0 10px 25px rgba(102, 126, 234, 0.3)",
                  "0 15px 35px rgba(102, 126, 234, 0.5)",
                  "0 10px 25px rgba(102, 126, 234, 0.3)"
                ]
              }}
              transition={{
                boxShadow: { duration: 2, repeat: Infinity },
                default: { type: "spring" }
              }}
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <Zap className="w-8 h-8" />
              </motion.div>
              <span>
                {!showMoodSelector ? 'Shift Now' :
                 !showTimeSelector ? 'Continue' :
                 'Start Activity'}
              </span>
            </motion.button>
          </motion.div>

          {/* Reset/Back Button */}
          <AnimatePresence>
            {(showMoodSelector || showTimeSelector) && (
              <motion.button
                onClick={resetSelection}
                className="text-white/60 hover:text-white/80 text-sm underline"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                whileHover={{ scale: 1.05 }}
              >
                Start over
              </motion.button>
            )}
          </AnimatePresence>

          {/* Quick Stats */}
          <motion.div
            className="grid grid-cols-3 gap-6 mt-12 text-center"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.6 }}
          >
            {[
              { icon: Brain, value: "127", label: "Activities Done", color: "text-blue-400" },
              { icon: Heart, value: "5", label: "Day Streak", color: "text-pink-400" },
              { icon: Clock, value: "2.3h", label: "Time Shifted", color: "text-green-400" }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                className="glass rounded-xl p-4 hover-lift"
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.9 + (index * 0.1), type: "spring" }}
                whileHover={{
                  scale: 1.05,
                  backgroundColor: "rgba(255, 255, 255, 0.15)",
                  y: -5
                }}
              >
                <motion.div
                  animate={{
                    rotate: [0, 5, -5, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: index * 0.5
                  }}
                >
                  <stat.icon className={`w-8 h-8 mx-auto mb-2 ${stat.color}`} />
                </motion.div>
                <motion.div
                  className="text-2xl font-bold text-white"
                  animate={{ scale: [1, 1.05, 1] }}
                  transition={{ duration: 2, repeat: Infinity, delay: index * 0.3 }}
                >
                  {stat.value}
                </motion.div>
                <div className="text-sm text-white/60">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </main>

      {/* Footer */}
      <footer className="p-6 text-center">
        <p className="text-white/40 text-sm">
          "You're a Builder, not a Scroller" ✨
        </p>
      </footer>
    </div>
  );
}
