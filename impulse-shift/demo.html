<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ImpulseShift - Rewire Your Scroll</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'shimmer': 'shimmer 2s linear infinite',
                        'bounce-gentle': 'bounceGentle 2s ease-in-out infinite',
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-up': 'slideUp 0.4s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                        'wiggle': 'wiggle 1s ease-in-out infinite',
                        'particle': 'particle 3s ease-out infinite',
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(236, 72, 153, 0.5); }
            to { box-shadow: 0 0 30px rgba(236, 72, 153, 0.8), 0 0 40px rgba(139, 92, 246, 0.3); }
        }
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        @keyframes bounceGentle {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes scaleIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }
        @keyframes wiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(-3deg); }
            75% { transform: rotate(3deg); }
        }
        @keyframes particle {
            0% { transform: translateY(0) scale(1); opacity: 1; }
            100% { transform: translateY(-100px) scale(0); opacity: 0; }
        }

        .slide-in { animation: slideInFromBottom 0.3s ease-out; }
        .glass { backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.1); }
        .gradient-text { background: linear-gradient(135deg, #ec4899, #8b5cf6, #06b6d4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; }
        .shimmer-bg { background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent); background-size: 200% 100%; }
        .particle { position: absolute; width: 4px; height: 4px; background: radial-gradient(circle, #ec4899, #8b5cf6); border-radius: 50%; pointer-events: none; }

        /* Custom scrollbar */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: rgba(255, 255, 255, 0.1); border-radius: 4px; }
        ::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #ec4899, #8b5cf6); border-radius: 4px; }
        ::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #db2777, #7c3aed); }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 relative overflow-x-hidden">
    <!-- Animated Background Elements -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-pink-500/20 to-violet-500/20 rounded-full blur-xl animate-float"></div>
        <div class="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-full blur-xl animate-float" style="animation-delay: -2s;"></div>
        <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-gradient-to-r from-violet-500/20 to-pink-500/20 rounded-full blur-xl animate-float" style="animation-delay: -4s;"></div>
        <div class="absolute bottom-20 right-1/3 w-28 h-28 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 rounded-full blur-xl animate-float" style="animation-delay: -1s;"></div>
    </div>

    <div id="app" class="relative z-10">
        <!-- Header -->
        <header class="p-6 flex justify-between items-center animate-fade-in">
            <div class="flex items-center gap-3 group">
                <div class="w-12 h-12 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 animate-glow">
                    <svg class="w-7 h-7 text-white animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h1 class="text-3xl font-bold gradient-text animate-shimmer shimmer-bg">ImpulseShift</h1>
            </div>

            <div class="flex items-center gap-6 text-white/90">
                <div class="flex items-center gap-2 glass rounded-full px-4 py-2 hover:bg-white/10 transition-all duration-300 cursor-pointer group">
                    <svg class="w-5 h-5 text-yellow-400 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                    <span class="text-sm font-semibold">1,247</span>
                    <span class="text-xs text-white/60">coins</span>
                </div>
                <div class="flex items-center gap-2 glass rounded-full px-4 py-2 hover:bg-white/10 transition-all duration-300 cursor-pointer group">
                    <svg class="w-5 h-5 text-orange-400 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                    <span class="text-sm font-semibold">5</span>
                    <span class="text-xs text-white/60">day streak</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 flex flex-col items-center justify-center p-8 text-center">
            <div class="max-w-4xl mx-auto">
                <!-- Main Message -->
                <div class="mb-16 animate-slide-up">
                    <div class="relative">
                        <h2 class="text-6xl md:text-7xl font-black text-white mb-6 leading-tight">
                            Let's <span class="gradient-text animate-shimmer shimmer-bg">Shift</span> Your Impulse
                        </h2>
                        <!-- Floating particles around the title -->
                        <div class="absolute -top-4 left-1/4 particle animate-particle" style="animation-delay: 0s;"></div>
                        <div class="absolute -top-2 right-1/3 particle animate-particle" style="animation-delay: 1s;"></div>
                        <div class="absolute top-8 left-1/3 particle animate-particle" style="animation-delay: 2s;"></div>
                    </div>
                    <p class="text-2xl text-white/90 mb-4 font-medium">
                        Transform mindless scrolling into something <span class="text-pink-300 font-semibold">amazing</span>
                    </p>
                    <div class="glass rounded-2xl p-6 mb-4 hover:bg-white/10 transition-all duration-300 cursor-pointer group">
                        <p class="text-lg text-white/70 group-hover:text-white/90 transition-colors">
                            You've avoided <span class="font-bold text-emerald-300 text-xl">47 minutes</span> of doomscrolling this week!
                            <span class="inline-block animate-bounce-gentle">🎉</span>
                        </p>
                        <div class="mt-2 w-full bg-white/20 rounded-full h-2">
                            <div class="bg-gradient-to-r from-emerald-400 to-cyan-400 h-2 rounded-full w-3/4 animate-shimmer shimmer-bg"></div>
                        </div>
                    </div>
                </div>

                <!-- Mood Selection -->
                <div id="mood-selector" class="mb-12 hidden animate-slide-up">
                    <h3 class="text-3xl font-bold text-white mb-8 gradient-text">How are you feeling?</h3>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-6 mb-8">
                        <button onclick="selectMood('energetic')" class="mood-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-yellow-500/20 hover:to-orange-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="text-4xl mb-3 group-hover:animate-bounce-gentle">⚡</div>
                            <div class="text-white font-semibold text-lg">Energetic</div>
                            <div class="text-white/60 text-sm mt-1">Ready to conquer!</div>
                        </button>
                        <button onclick="selectMood('calm')" class="mood-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-blue-500/20 hover:to-cyan-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="text-4xl mb-3 group-hover:animate-bounce-gentle">🌊</div>
                            <div class="text-white font-semibold text-lg">Calm</div>
                            <div class="text-white/60 text-sm mt-1">Peaceful vibes</div>
                        </button>
                        <button onclick="selectMood('focused')" class="mood-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-green-500/20 hover:to-emerald-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="text-4xl mb-3 group-hover:animate-bounce-gentle">🎯</div>
                            <div class="text-white font-semibold text-lg">Focused</div>
                            <div class="text-white/60 text-sm mt-1">Laser sharp</div>
                        </button>
                        <button onclick="selectMood('creative')" class="mood-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-purple-500/20 hover:to-pink-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="text-4xl mb-3 group-hover:animate-bounce-gentle">🎨</div>
                            <div class="text-white font-semibold text-lg">Creative</div>
                            <div class="text-white/60 text-sm mt-1">Artistic flow</div>
                        </button>
                        <button onclick="selectMood('stressed')" class="mood-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-red-500/20 hover:to-pink-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="text-4xl mb-3 group-hover:animate-bounce-gentle">😤</div>
                            <div class="text-white font-semibold text-lg">Stressed</div>
                            <div class="text-white/60 text-sm mt-1">Need relief</div>
                        </button>
                        <button onclick="selectMood('bored')" class="mood-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-gray-500/20 hover:to-slate-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="text-4xl mb-3 group-hover:animate-bounce-gentle">😴</div>
                            <div class="text-white font-semibold text-lg">Bored</div>
                            <div class="text-white/60 text-sm mt-1">Need excitement</div>
                        </button>
                    </div>
                    <button onclick="selectMood('focused')" class="glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 font-medium">
                        ✨ Skip - surprise me!
                    </button>
                </div>

                <!-- Time Selection -->
                <div id="time-selector" class="mb-12 hidden animate-slide-up">
                    <h3 class="text-3xl font-bold text-white mb-8 gradient-text">How much time do you have?</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                        <button onclick="selectTime(1)" class="time-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-emerald-500/20 hover:to-teal-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="relative">
                                <svg class="w-8 h-8 mx-auto mb-3 text-emerald-400 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div class="absolute -top-1 -right-1 w-3 h-3 bg-emerald-400 rounded-full animate-pulse"></div>
                            </div>
                            <div class="text-white font-semibold text-lg">1 min</div>
                            <div class="text-white/60 text-sm mt-1">Quick boost</div>
                        </button>
                        <button onclick="selectTime(3)" class="time-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-blue-500/20 hover:to-cyan-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="relative">
                                <svg class="w-8 h-8 mx-auto mb-3 text-blue-400 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div class="absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                            </div>
                            <div class="text-white font-semibold text-lg">3 min</div>
                            <div class="text-white/60 text-sm mt-1">Perfect break</div>
                        </button>
                        <button onclick="selectTime(5)" class="time-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-purple-500/20 hover:to-pink-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="relative">
                                <svg class="w-8 h-8 mx-auto mb-3 text-purple-400 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div class="absolute -top-1 -right-1 w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
                            </div>
                            <div class="text-white font-semibold text-lg">5 min</div>
                            <div class="text-white/60 text-sm mt-1">Deep dive</div>
                        </button>
                        <button onclick="selectTime(10)" class="time-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-orange-500/20 hover:to-red-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="relative">
                                <svg class="w-8 h-8 mx-auto mb-3 text-orange-400 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div class="absolute -top-1 -right-1 w-3 h-3 bg-orange-400 rounded-full animate-pulse"></div>
                            </div>
                            <div class="text-white font-semibold text-lg">10+ min</div>
                            <div class="text-white/60 text-sm mt-1">Full experience</div>
                        </button>
                    </div>
                </div>

                <!-- Main Action Button -->
                <div class="mb-8">
                    <button id="main-btn" onclick="handleShiftNow()" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-6 px-12 rounded-full text-2xl shadow-2xl transform hover:scale-105 transition-all duration-200 flex items-center gap-3 mx-auto">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span id="btn-text">Shift Now</span>
                    </button>
                </div>

                <!-- Reset/Back Button -->
                <button id="reset-btn" onclick="resetSelection()" class="text-white/60 hover:text-white/80 text-sm underline hidden">
                    Start over
                </button>

                <!-- Quick Stats -->
                <div class="grid grid-cols-3 gap-6 mt-12 text-center">
                    <div class="bg-white/10 rounded-xl p-4">
                        <svg class="w-8 h-8 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        <div class="text-2xl font-bold text-white">127</div>
                        <div class="text-sm text-white/60">Activities Done</div>
                    </div>
                    <div class="bg-white/10 rounded-xl p-4">
                        <svg class="w-8 h-8 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        <div class="text-2xl font-bold text-white">5</div>
                        <div class="text-sm text-white/60">Day Streak</div>
                    </div>
                    <div class="bg-white/10 rounded-xl p-4">
                        <svg class="w-8 h-8 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div class="text-2xl font-bold text-white">2.3h</div>
                        <div class="text-sm text-white/60">Time Shifted</div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="p-6 text-center">
            <p class="text-white/40 text-sm">
                "You're a Builder, not a Scroller" ✨
            </p>
        </footer>
    </div>

    <script>
        let selectedMood = null;
        let selectedTime = null;
        let showMoodSelector = false;
        let showTimeSelector = false;

        function handleShiftNow() {
            if (!showMoodSelector && !showTimeSelector) {
                showMoodSelector = true;
                document.getElementById('mood-selector').classList.remove('hidden');
                document.getElementById('btn-text').textContent = 'Continue';
                document.getElementById('reset-btn').classList.remove('hidden');
                return;
            }
            
            if (showMoodSelector && !selectedMood) {
                return;
            }
            
            if (!showTimeSelector) {
                showTimeSelector = true;
                document.getElementById('time-selector').classList.remove('hidden');
                document.getElementById('btn-text').textContent = 'Start Activity';
                return;
            }
            
            if (!selectedTime) {
                return;
            }
            
            // Simulate starting activity
            startActivity();
        }

        function selectMood(mood) {
            selectedMood = mood;
            // Update UI to show selection
            document.querySelectorAll('.mood-btn').forEach(btn => {
                btn.classList.remove('border-white', 'bg-white/20', 'scale-105');
                btn.classList.add('border-white/30', 'bg-white/10');
            });
            event.target.closest('.mood-btn').classList.add('border-white', 'bg-white/20', 'scale-105');
            event.target.closest('.mood-btn').classList.remove('border-white/30', 'bg-white/10');
        }

        function selectTime(time) {
            selectedTime = time;
            // Update UI to show selection
            document.querySelectorAll('.time-btn').forEach(btn => {
                btn.classList.remove('border-white', 'bg-white/20', 'scale-105');
                btn.classList.add('border-white/30', 'bg-white/10');
            });
            event.target.closest('.time-btn').classList.add('border-white', 'bg-white/20', 'scale-105');
            event.target.closest('.time-btn').classList.remove('border-white/30', 'bg-white/10');
        }

        function resetSelection() {
            selectedMood = null;
            selectedTime = null;
            showMoodSelector = false;
            showTimeSelector = false;
            
            document.getElementById('mood-selector').classList.add('hidden');
            document.getElementById('time-selector').classList.add('hidden');
            document.getElementById('btn-text').textContent = 'Shift Now';
            document.getElementById('reset-btn').classList.add('hidden');
            
            // Reset button states
            document.querySelectorAll('.mood-btn, .time-btn').forEach(btn => {
                btn.classList.remove('border-white', 'bg-white/20', 'scale-105');
                btn.classList.add('border-white/30', 'bg-white/10');
            });
        }

        // Curated Working Activities Database
        const activities = {
            bored: [
                // All Working Activities
                { id: 'color-clash', name: 'Color Clash', description: 'Word says "Blue" but text is red - choose the actual color!', icon: '🌈', category: 'Brain Teaser', difficulty: 'Medium' },
                { id: 'would-you-rather', name: 'Would You Rather?', description: 'Funny or deep rapid-fire choices!', icon: '🤔', category: 'Decision Game', difficulty: 'Easy' },
                { id: 'emoji-decode', name: 'Emoji Decode', description: 'Guess the phrase from emoji clues!', icon: '😎', category: 'Word Game', difficulty: 'Medium' },
                { id: 'pattern-tap', name: 'Pattern Tap', description: 'Remember and repeat the color sequence!', icon: '🎮', category: 'Memory Game', difficulty: 'Medium' },
                { id: 'reaction-spark', name: 'Reaction Spark', description: 'Test your lightning-fast reflexes!', icon: '⚡', category: 'Speed Game', difficulty: 'Medium' },
                { id: 'trivia-blast', name: 'Trivia Blast', description: 'Quick-fire trivia questions on random topics!', icon: '🧠', category: 'Knowledge', difficulty: 'Medium' },
                { id: 'word-scramble', name: 'Word Scramble', description: 'Unscramble letters to form words!', icon: '🔤', category: 'Word Game', difficulty: 'Medium' },
                { id: 'number-ninja', name: 'Number Ninja', description: 'Quick math challenges to sharpen your mind!', icon: '🥷', category: 'Math Game', difficulty: 'Medium' },
                { id: 'rhyme-time', name: 'Rhyme Time', description: 'Find words that rhyme with the given word!', icon: '🎵', category: 'Word Game', difficulty: 'Easy' },
                { id: 'micro-learning', name: '60-Second Science', description: 'Learn fascinating facts in bite-sized pieces!', icon: '🧠', category: 'Learning', difficulty: 'Easy' }
            ],

            stressed: [
                // Working Stress Relief Activities
                { id: 'box-breathing', name: 'Box Breathing', description: 'Animated square breathing exercise', icon: '🫁', category: 'Breathing', difficulty: 'Easy' },
                { id: 'gratitude-drop', name: 'Gratitude Drop', description: 'Write down things you\'re grateful for', icon: '💖', category: 'Reflection', difficulty: 'Easy' },
                { id: 'pattern-tap', name: 'Mindful Pattern Tap', description: 'Focus your mind with gentle memory patterns', icon: '🎮', category: 'Mindful Game', difficulty: 'Easy' },
                { id: 'micro-learning', name: 'Calming Science', description: 'Learn peaceful facts about nature', icon: '🧠', category: 'Learning', difficulty: 'Easy' },
                { id: 'would-you-rather', name: 'Gentle Choices', description: 'Peaceful decision-making exercise', icon: '🤔', category: 'Reflection', difficulty: 'Easy' }
            ],

            energetic: [
                // High Energy Working Activities
                { id: 'reaction-spark', name: 'Reaction Spark', description: 'Test your lightning-fast reflexes!', icon: '⚡', category: 'Speed Game', difficulty: 'Medium' },
                { id: 'color-clash', name: 'Color Clash', description: 'Fast-paced brain teaser challenge!', icon: '🌈', category: 'Brain Teaser', difficulty: 'Medium' },
                { id: 'pattern-tap', name: 'Speed Pattern Tap', description: 'Quick memory challenges!', icon: '🎮', category: 'Memory Game', difficulty: 'Medium' },
                { id: 'number-ninja', name: 'Math Lightning', description: 'Solve equations at lightning speed!', icon: '🥷', category: 'Speed Math', difficulty: 'Hard' },
                { id: 'word-scramble', name: 'Speed Unscramble', description: 'Unscramble words as fast as possible!', icon: '🔤', category: 'Speed Word', difficulty: 'Medium' },
                { id: 'trivia-blast', name: 'Lightning Trivia', description: 'Rapid-fire knowledge questions!', icon: '🧠', category: 'Speed Knowledge', difficulty: 'Medium' },
                { id: 'rhyme-time', name: 'Rhyme Rush', description: 'Find rhymes at breakneck speed!', icon: '🎵', category: 'Speed Word', difficulty: 'Medium' }
            ],

            creative: [
                // Creative Working Activities
                { id: 'emoji-decode', name: 'Creative Emoji Puzzles', description: 'Decode creative stories from emojis!', icon: '😎', category: 'Creative Puzzle', difficulty: 'Medium' },
                { id: 'would-you-rather', name: 'Imagination Choices', description: 'Spark creativity with wild scenarios!', icon: '🤔', category: 'Creative Thinking', difficulty: 'Easy' },
                { id: 'word-scramble', name: 'Creative Unscramble', description: 'Unscramble words to unlock stories!', icon: '🔤', category: 'Creative Word', difficulty: 'Medium' },
                { id: 'rhyme-time', name: 'Poetry Rhymes', description: 'Create beautiful rhyming patterns!', icon: '🎵', category: 'Poetry', difficulty: 'Easy' },
                { id: 'trivia-blast', name: 'Creative Trivia', description: 'Art, music, and culture questions!', icon: '🧠', category: 'Creative Knowledge', difficulty: 'Medium' }
            ],

            focused: [
                // Focus Training Working Activities
                { id: 'pattern-tap', name: 'Focus Pattern Training', description: 'Sharpen your focus and memory!', icon: '🎮', category: 'Memory Training', difficulty: 'Medium' },
                { id: 'color-clash', name: 'Attention Color Test', description: 'Train your attention and focus!', icon: '🌈', category: 'Attention Training', difficulty: 'Medium' },
                { id: 'micro-learning', name: 'Focused Learning', description: 'Concentrate on fascinating facts!', icon: '🧠', category: 'Learning', difficulty: 'Easy' },
                { id: 'number-ninja', name: 'Math Focus Training', description: 'Sharpen focus with math challenges!', icon: '🥷', category: 'Cognitive Training', difficulty: 'Medium' },
                { id: 'trivia-blast', name: 'Knowledge Focus', description: 'Concentrate on learning new facts!', icon: '🧠', category: 'Focus Training', difficulty: 'Medium' },
                { id: 'reaction-spark', name: 'Attention Reaction', description: 'Focus your reflexes and attention!', icon: '⚡', category: 'Focus Game', difficulty: 'Medium' }
            ],

            calm: [
                // Calming Working Activities
                { id: 'gratitude-drop', name: 'Peaceful Gratitude', description: 'Reflect on life\'s blessings', icon: '💖', category: 'Reflection', difficulty: 'Easy' },
                { id: 'box-breathing', name: 'Calming Breath', description: 'Gentle breathing exercise', icon: '🫁', category: 'Breathing', difficulty: 'Easy' },
                { id: 'micro-learning', name: 'Gentle Learning', description: 'Learn peaceful facts about nature', icon: '🧠', category: 'Calm Learning', difficulty: 'Easy' },
                { id: 'pattern-tap', name: 'Meditative Patterns', description: 'Gentle memory patterns for peace', icon: '🎮', category: 'Mindful Game', difficulty: 'Easy' },
                { id: 'would-you-rather', name: 'Peaceful Choices', description: 'Gentle decision-making exercise', icon: '🤔', category: 'Calm Reflection', difficulty: 'Easy' }
            ]
        };

        // Additional activity pools for even more variety
        const bonusActivities = {
            quickGames: [
                { id: 'lightning-math', name: 'Lightning Math', description: 'Solve equations in under 3 seconds!', icon: '⚡', category: 'Speed Math', difficulty: 'Hard' },
                { id: 'word-avalanche', name: 'Word Avalanche', description: 'Catch falling letters to make words!', icon: '❄️', category: 'Word Catch', difficulty: 'Medium' },
                { id: 'color-tornado', name: 'Color Tornado', description: 'Sort colors in a spinning vortex!', icon: '🌪️', category: 'Color Sorting', difficulty: 'Hard' },
                { id: 'memory-matrix', name: 'Memory Matrix', description: 'Remember positions in an expanding grid!', icon: '🔲', category: 'Spatial Memory', difficulty: 'Hard' },
                { id: 'rhythm-rain', name: 'Rhythm Rain', description: 'Catch musical notes falling from the sky!', icon: '🎵', category: 'Music Game', difficulty: 'Medium' }
            ],

            mindfulMoments: [
                { id: 'breath-waves', name: 'Breath Waves', description: 'Watch waves sync with your breathing', icon: '🌊', category: 'Breathing Visual', difficulty: 'Easy' },
                { id: 'gratitude-tree', name: 'Gratitude Tree', description: 'Grow a tree with your grateful thoughts', icon: '🌳', category: 'Gratitude Practice', difficulty: 'Easy' },
                { id: 'mindful-maze', name: 'Mindful Maze', description: 'Navigate mazes with slow, deliberate moves', icon: '🌀', category: 'Mindful Navigation', difficulty: 'Easy' },
                { id: 'emotion-wheel', name: 'Emotion Wheel', description: 'Explore and name your current emotions', icon: '🎡', category: 'Emotional Awareness', difficulty: 'Easy' },
                { id: 'present-moment', name: 'Present Moment', description: 'Focus entirely on the here and now', icon: '⏰', category: 'Presence Practice', difficulty: 'Easy' }
            ],

            creativeSparks: [
                { id: 'random-inventor', name: 'Random Inventor', description: 'Invent solutions for absurd problems!', icon: '💡', category: 'Problem Solving', difficulty: 'Medium' },
                { id: 'story-dice', name: 'Story Dice', description: 'Create stories from random image combinations!', icon: '🎲', category: 'Storytelling', difficulty: 'Easy' },
                { id: 'color-symphony', name: 'Color Symphony', description: 'Paint with music and sound!', icon: '🎨', category: 'Synesthetic Art', difficulty: 'Medium' },
                { id: 'dream-architect', name: 'Dream Architect', description: 'Design impossible buildings and spaces!', icon: '🏗️', category: 'Architecture', difficulty: 'Medium' },
                { id: 'time-traveler', name: 'Time Traveler', description: 'Imagine visiting any time period!', icon: '⏳', category: 'Historical Fiction', difficulty: 'Easy' }
            ],

            brainBoosters: [
                { id: 'logic-labyrinth', name: 'Logic Labyrinth', description: 'Solve increasingly complex logic puzzles!', icon: '🧩', category: 'Logic Puzzle', difficulty: 'Hard' },
                { id: 'pattern-prophet', name: 'Pattern Prophet', description: 'Predict complex mathematical sequences!', icon: '🔮', category: 'Pattern Recognition', difficulty: 'Hard' },
                { id: 'memory-mansion', name: 'Memory Mansion', description: 'Navigate a house using only memory!', icon: '🏚️', category: 'Spatial Memory', difficulty: 'Hard' },
                { id: 'word-wizard', name: 'Word Wizard', description: 'Transform words through letter changes!', icon: '🧙‍♂️', category: 'Word Transformation', difficulty: 'Medium' },
                { id: 'number-ninja-advanced', name: 'Number Ninja Pro', description: 'Master advanced mathematical concepts!', icon: '🥷', category: 'Advanced Math', difficulty: 'Hard' }
            ],

            socialConnectors: [
                { id: 'empathy-builder', name: 'Empathy Builder', description: 'Understand different perspectives on situations!', icon: '🤝', category: 'Social Skills', difficulty: 'Medium' },
                { id: 'conversation-starter', name: 'Conversation Starter', description: 'Practice interesting conversation topics!', icon: '💬', category: 'Communication', difficulty: 'Easy' },
                { id: 'cultural-explorer', name: 'Cultural Explorer', description: 'Learn about traditions from around the world!', icon: '🌍', category: 'Cultural Learning', difficulty: 'Easy' },
                { id: 'kindness-generator', name: 'Kindness Generator', description: 'Brainstorm ways to spread kindness!', icon: '💝', category: 'Compassion Practice', difficulty: 'Easy' },
                { id: 'perspective-shifter', name: 'Perspective Shifter', description: 'See situations from multiple viewpoints!', icon: '👁️‍🗨️', category: 'Critical Thinking', difficulty: 'Medium' }
            ],

            physicalWellness: [
                { id: 'desk-yoga', name: 'Desk Yoga', description: 'Simple stretches you can do anywhere!', icon: '🧘‍♀️', category: 'Physical Wellness', difficulty: 'Easy' },
                { id: 'eye-exercises', name: 'Eye Exercises', description: 'Relieve eye strain with guided exercises!', icon: '👁️', category: 'Eye Health', difficulty: 'Easy' },
                { id: 'posture-check', name: 'Posture Check', description: 'Improve your sitting and standing posture!', icon: '🏃‍♂️', category: 'Posture Health', difficulty: 'Easy' },
                { id: 'hand-stretches', name: 'Hand Stretches', description: 'Prevent repetitive strain with hand exercises!', icon: '✋', category: 'Hand Health', difficulty: 'Easy' },
                { id: 'breathing-energy', name: 'Energizing Breath', description: 'Breathing techniques to boost energy!', icon: '💨', category: 'Energy Boost', difficulty: 'Easy' }
            ]
        };

        // Game state variables
        let gameState = {
            sequence: [],
            userSequence: [],
            score: 0,
            timeRemaining: 0,
            isActive: false,
            isCompleted: false,
            showingSequence: false,
            currentStep: 0
        };

        const colors = [
            { id: 0, name: 'Red', bg: 'bg-red-500', hover: 'hover:bg-red-600' },
            { id: 1, name: 'Blue', bg: 'bg-blue-500', hover: 'hover:bg-blue-600' },
            { id: 2, name: 'Green', bg: 'bg-green-500', hover: 'hover:bg-green-600' },
            { id: 3, name: 'Yellow', bg: 'bg-yellow-500', hover: 'hover:bg-yellow-600' }
        ];

        // Track shown activities to prevent immediate repeats
        let shownActivities = [];
        const MAX_RECENT_ACTIVITIES = 10;

        function getRandomActivity(mood) {
            // Get all working activities (ones with actual implementations)
            const workingActivities = [
                'pattern-tap', 'color-clash', 'would-you-rather', 'emoji-decode',
                'reaction-spark', 'box-breathing', 'gratitude-drop', 'micro-learning',
                'trivia-blast', 'word-scramble', 'number-ninja', 'rhyme-time'
            ];

            // Filter mood-appropriate working activities
            const moodActivities = activities[mood] || activities['focused'];
            const availableActivities = moodActivities.filter(activity =>
                workingActivities.includes(activity.id) &&
                !shownActivities.includes(activity.id)
            );

            // If we've shown all activities, reset the list but keep the last 3
            if (availableActivities.length === 0) {
                shownActivities = shownActivities.slice(-3);
                const resetAvailable = moodActivities.filter(activity =>
                    workingActivities.includes(activity.id) &&
                    !shownActivities.includes(activity.id)
                );

                if (resetAvailable.length > 0) {
                    const selected = resetAvailable[Math.floor(Math.random() * resetAvailable.length)];
                    shownActivities.push(selected.id);
                    if (shownActivities.length > MAX_RECENT_ACTIVITIES) {
                        shownActivities = shownActivities.slice(-MAX_RECENT_ACTIVITIES);
                    }
                    return selected;
                }
            }

            // Select from available activities
            if (availableActivities.length > 0) {
                const selected = availableActivities[Math.floor(Math.random() * availableActivities.length)];
                shownActivities.push(selected.id);
                if (shownActivities.length > MAX_RECENT_ACTIVITIES) {
                    shownActivities = shownActivities.slice(-MAX_RECENT_ACTIVITIES);
                }
                return selected;
            }

            // Fallback to any working activity
            const fallbackActivities = moodActivities.filter(activity =>
                workingActivities.includes(activity.id)
            );
            return fallbackActivities[Math.floor(Math.random() * fallbackActivities.length)];
        }

        function startActivity() {
            const selectedActivity = getRandomActivity(selectedMood);

            // Show beautiful activity selection interface
            document.querySelector('main').innerHTML = `
                <div class="flex-1 p-6 animate-fade-in">
                    <div class="max-w-4xl mx-auto">
                        <!-- Activity Header -->
                        <div class="flex justify-between items-center mb-8">
                            <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                <span class="font-medium">Back Home</span>
                            </button>

                            <div class="text-center">
                                <div class="text-4xl mb-2 animate-bounce-gentle">${selectedActivity.icon}</div>
                                <h1 class="text-2xl font-bold text-white">${selectedActivity.name}</h1>
                                <p class="text-white/60 text-sm">${selectedActivity.category} • ${selectedActivity.difficulty}</p>
                            </div>

                            <button onclick="startActivity()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span class="font-medium">Try Another</span>
                            </button>
                        </div>

                        <!-- Activity Card -->
                        <div class="glass rounded-3xl p-8 mb-8 hover:bg-white/10 transition-all duration-500 group">
                            <div class="text-center mb-8">
                                <div class="text-6xl mb-4 group-hover:animate-bounce-gentle">${selectedActivity.icon}</div>
                                <h2 class="text-3xl font-bold text-white mb-3 gradient-text">${selectedActivity.name}</h2>
                                <p class="text-xl text-white/80 mb-6">${selectedActivity.description}</p>

                                <!-- Activity Stats -->
                                <div class="flex justify-center gap-6 mb-8">
                                    <div class="glass rounded-2xl px-6 py-3">
                                        <div class="text-2xl font-bold text-white">${selectedTime}</div>
                                        <div class="text-white/60 text-sm">minutes</div>
                                    </div>
                                    <div class="glass rounded-2xl px-6 py-3">
                                        <div class="text-2xl font-bold text-emerald-400">+15</div>
                                        <div class="text-white/60 text-sm">coins</div>
                                    </div>
                                    <div class="glass rounded-2xl px-6 py-3">
                                        <div class="text-2xl font-bold text-purple-400">${selectedActivity.difficulty}</div>
                                        <div class="text-white/60 text-sm">level</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Start Button -->
                            <div class="text-center">
                                <button onclick="startSpecificActivity('${selectedActivity.id}')" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-12 rounded-full text-xl shadow-2xl transform hover:scale-105 transition-all duration-300 animate-glow group">
                                    <div class="flex items-center gap-3">
                                        <svg class="w-6 h-6 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.5a2.5 2.5 0 110 5H9V10z"></path>
                                        </svg>
                                        <span>Start ${selectedActivity.name}</span>
                                        <svg class="w-6 h-6 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                    </div>
                                </button>
                            </div>
                        </div>

                        <!-- Mood-based Suggestions -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold text-white mb-6 text-center">More ${selectedMood.charAt(0).toUpperCase() + selectedMood.slice(1)} Activities</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                ${activities[selectedMood].slice(0, 3).map(activity => `
                                    <div class="glass rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 cursor-pointer group" onclick="startSpecificActivity('${activity.id}')">
                                        <div class="text-center">
                                            <div class="text-3xl mb-3 group-hover:animate-bounce-gentle">${activity.icon}</div>
                                            <h4 class="text-white font-semibold mb-2">${activity.name}</h4>
                                            <p class="text-white/60 text-sm">${activity.description}</p>
                                            <div class="mt-3 text-xs text-white/50">${activity.category}</div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function startSpecificActivity(activityId) {
            switch(activityId) {
                case 'pattern-tap':
                    startPatternTapGame();
                    break;
                case 'color-clash':
                    startColorClashGame();
                    break;
                case 'would-you-rather':
                    startWouldYouRatherGame();
                    break;
                case 'emoji-decode':
                    startEmojiDecodeGame();
                    break;
                case 'box-breathing':
                    startBoxBreathingActivity();
                    break;
                case 'gratitude-drop':
                    startGratitudeDropActivity();
                    break;
                case 'reaction-spark':
                    startReactionSparkGame();
                    break;
                case 'micro-learning':
                    startMicroLearningActivity();
                    break;
                case 'trivia-blast':
                    startTriviaBlastGame();
                    break;
                case 'word-scramble':
                    startWordScrambleGame();
                    break;
                case 'number-ninja':
                    startNumberNinjaGame();
                    break;
                case 'rhyme-time':
                    startRhymeTimeGame();
                    break;
                default:
                    // For other activities, show a coming soon message
                    showComingSoon(activityId);
            }
        }

        function showComingSoon(activityId) {
            const activity = Object.values(activities).flat().find(a => a.id === activityId);

            document.querySelector('main').innerHTML = `
                <div class="flex-1 flex items-center justify-center p-8">
                    <div class="max-w-2xl mx-auto text-center animate-scale-in">
                        <div class="glass rounded-3xl p-12">
                            <div class="text-8xl mb-6 animate-bounce-gentle">${activity.icon}</div>
                            <h2 class="text-4xl font-bold text-white mb-4 gradient-text">${activity.name}</h2>
                            <p class="text-xl text-white/80 mb-8">${activity.description}</p>

                            <div class="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-2xl p-6 mb-8">
                                <div class="text-4xl mb-3">🚀</div>
                                <h3 class="text-2xl font-bold text-white mb-2">Coming Soon!</h3>
                                <p class="text-white/70">This amazing activity is being crafted with love. Try our Pattern Tap game for now!</p>
                            </div>

                            <div class="space-y-4">
                                <button onclick="startSpecificActivity('pattern-tap')" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                    🎮 Try Pattern Tap Instead
                                </button>
                                <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                    🏠 Back to Home
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function startPatternTapGame() {
            gameState.timeRemaining = selectedTime * 60; // Convert minutes to seconds

            // Show enhanced Pattern Tap game interface
            document.querySelector('main').innerHTML = `
                <div class="flex-1 p-6 animate-fade-in">
                    <div class="max-w-4xl mx-auto">
                        <!-- Enhanced Game Header -->
                        <div class="flex justify-between items-center mb-8">
                            <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                <span class="font-medium">Back Home</span>
                            </button>

                            <div class="text-center">
                                <div class="text-4xl mb-2 animate-bounce-gentle">🎮</div>
                                <h1 class="text-3xl font-bold gradient-text">Pattern Tap</h1>
                                <p class="text-white/60">Memory Challenge • Medium</p>
                            </div>

                            <button onclick="startActivity()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span class="font-medium">New Game</span>
                            </button>
                        </div>

                        <!-- Enhanced Game Content -->
                        <div class="glass rounded-3xl p-8 mb-8">
                            <!-- Game Description -->
                            <div class="text-center mb-8">
                                <p class="text-xl text-white/90 mb-4">
                                    🧠 <strong>Challenge your memory!</strong> Watch the pattern, then repeat it perfectly.
                                </p>
                                <div class="flex justify-center gap-4 text-sm text-white/60">
                                    <span>🎯 Focus</span>
                                    <span>⚡ Speed</span>
                                    <span>🧩 Memory</span>
                                </div>
                            </div>

                            <!-- Enhanced Game Stats -->
                            <div class="grid grid-cols-3 gap-6 mb-8">
                                <div class="glass rounded-2xl p-4 text-center">
                                    <div class="text-2xl font-bold text-white">
                                        <span id="score">0</span>
                                    </div>
                                    <div class="text-white/60 text-sm">Score</div>
                                </div>
                                <div class="glass rounded-2xl p-4 text-center">
                                    <div class="text-2xl font-bold text-emerald-400">
                                        <span id="timer">${formatTime(gameState.timeRemaining)}</span>
                                    </div>
                                    <div class="text-white/60 text-sm">Time Left</div>
                                </div>
                                <div class="glass rounded-2xl p-4 text-center">
                                    <div class="text-2xl font-bold text-purple-400">
                                        <span id="level">1</span>
                                    </div>
                                    <div class="text-white/60 text-sm">Level</div>
                                </div>
                            </div>

                            <!-- Enhanced Message Display -->
                            <div class="text-center mb-8">
                                <div class="glass rounded-2xl p-6">
                                    <div class="text-2xl font-bold text-white mb-2">
                                        <span id="game-message">Ready to challenge your memory?</span>
                                    </div>
                                    <div class="text-white/60">
                                        <span id="game-subtitle">Tap Start Game when you're ready!</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Color Buttons -->
                            <div class="grid grid-cols-2 gap-6 mb-8 max-w-md mx-auto">
                                ${colors.map(color => `
                                    <button
                                        id="color-${color.id}"
                                        onclick="handleColorClick(${color.id})"
                                        class="color-btn w-24 h-24 rounded-2xl transition-all duration-300 transform ${color.bg} ${color.hover} hover:scale-110 shadow-2xl glass border-2 border-white/20 hover:border-white/40 disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled
                                    >
                                        <span class="sr-only">${color.name}</span>
                                        <div class="w-full h-full rounded-2xl bg-gradient-to-br from-white/20 to-transparent"></div>
                                    </button>
                                `).join('')}
                            </div>

                            <!-- Enhanced Start/Status Section -->
                            <div class="text-center">
                                <button id="start-btn" onclick="startGame()" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-12 rounded-2xl text-xl shadow-2xl transform hover:scale-105 transition-all duration-300 animate-glow">
                                    <div class="flex items-center gap-3">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.5a2.5 2.5 0 110 5H9V10z"></path>
                                        </svg>
                                        <span>Start Game</span>
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                    </div>
                                </button>

                                <div id="completion-message" class="hidden animate-scale-in">
                                    <div class="glass rounded-3xl p-8 mt-8">
                                        <div class="text-6xl mb-4">🎉</div>
                                        <h3 class="text-3xl font-bold text-white mb-4">Incredible Memory!</h3>
                                        <p class="text-xl text-white/80 mb-2">Final Score: <span id="final-score" class="text-emerald-400 font-bold">0</span></p>
                                        <p class="text-white/60 mb-8">You've earned <span class="text-yellow-400 font-semibold">+25 coins</span>!</p>
                                        <div class="space-y-4">
                                            <button onclick="startGame()" class="w-full bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                                🎮 Play Again
                                            </button>
                                            <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                                🎯 Try Another Activity
                                            </button>
                                            <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                                🏠 Back to Home
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Instructions -->
                            <div id="instructions" class="mt-8 glass rounded-2xl p-6 text-center">
                                <h4 class="text-lg font-bold text-white mb-3">How to Play</h4>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-white/70">
                                    <div>
                                        <div class="text-2xl mb-2">👀</div>
                                        <p><strong>Watch</strong> the colors light up in sequence</p>
                                    </div>
                                    <div>
                                        <div class="text-2xl mb-2">👆</div>
                                        <p><strong>Repeat</strong> by tapping the same pattern</p>
                                    </div>
                                    <div>
                                        <div class="text-2xl mb-2">🚀</div>
                                        <p><strong>Level up</strong> with longer sequences!</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Start the game timer
            startGameTimer();
        }

        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }

        function startGameTimer() {
            const timer = setInterval(() => {
                if (gameState.isActive && gameState.timeRemaining > 0) {
                    gameState.timeRemaining--;
                    document.getElementById('timer').textContent = formatTime(gameState.timeRemaining);
                } else if (gameState.isActive && gameState.timeRemaining <= 0) {
                    endGame();
                    clearInterval(timer);
                } else if (gameState.isCompleted) {
                    clearInterval(timer);
                }
            }, 1000);
        }

        function generateSequence(length) {
            const sequence = [];
            for (let i = 0; i < length; i++) {
                sequence.push(Math.floor(Math.random() * colors.length));
            }
            return sequence;
        }

        function startGame() {
            gameState.isActive = true;
            gameState.score = 0;
            gameState.isCompleted = false;

            document.getElementById('start-btn').style.display = 'none';
            document.getElementById('instructions').style.display = 'none';

            startNewRound();
        }

        function startNewRound() {
            const sequenceLength = Math.min(3 + Math.floor(gameState.score / 2), 8);
            const level = Math.floor(gameState.score / 3) + 1;

            gameState.sequence = generateSequence(sequenceLength);
            gameState.userSequence = [];
            gameState.currentStep = 0;
            gameState.showingSequence = true;

            // Update UI with enhanced messaging
            document.getElementById('game-message').textContent = `Level ${level} - Watch carefully!`;
            document.getElementById('game-subtitle').textContent = `Sequence length: ${sequenceLength} colors`;
            document.getElementById('level').textContent = level;

            // Enhanced button styling during sequence display
            document.querySelectorAll('.color-btn').forEach(btn => {
                btn.disabled = true;
                btn.classList.add('cursor-not-allowed');
                btn.classList.remove('hover:scale-110');
                btn.style.transform = 'scale(0.95)';
                btn.style.opacity = '0.6';
            });

            // Show sequence with enhanced effects
            showSequence();
        }

        function showSequence() {
            let index = 0;

            function showNextColor() {
                if (index < gameState.sequence.length) {
                    const colorId = gameState.sequence[index];
                    const colorBtn = document.getElementById(`color-${colorId}`);

                    // Enhanced flash effect with multiple animations
                    colorBtn.style.transform = 'scale(1.2)';
                    colorBtn.style.opacity = '1';
                    colorBtn.classList.add('ring-4', 'ring-white', 'shadow-2xl');
                    colorBtn.style.boxShadow = '0 0 30px rgba(255, 255, 255, 0.8), 0 0 60px rgba(255, 255, 255, 0.4)';

                    // Add particle effect
                    createParticleEffect(colorBtn);

                    setTimeout(() => {
                        colorBtn.style.transform = 'scale(0.95)';
                        colorBtn.style.opacity = '0.6';
                        colorBtn.classList.remove('ring-4', 'ring-white', 'shadow-2xl');
                        colorBtn.style.boxShadow = '';

                        setTimeout(() => {
                            index++;
                            showNextColor();
                        }, 300);
                    }, 800);
                } else {
                    // Sequence shown, now wait for user input
                    gameState.showingSequence = false;
                    document.getElementById('game-message').textContent = 'Your turn! Repeat the pattern';
                    document.getElementById('game-subtitle').textContent = 'Tap the colors in the same order';

                    // Re-enable color buttons with enhanced styling
                    document.querySelectorAll('.color-btn').forEach(btn => {
                        btn.disabled = false;
                        btn.classList.remove('cursor-not-allowed');
                        btn.classList.add('hover:scale-110');
                        btn.style.transform = 'scale(1)';
                        btn.style.opacity = '1';
                    });
                }
            }

            setTimeout(showNextColor, 800);
        }

        function createParticleEffect(element) {
            const rect = element.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            for (let i = 0; i < 8; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle animate-particle';
                particle.style.left = centerX + 'px';
                particle.style.top = centerY + 'px';
                particle.style.animationDelay = (i * 0.1) + 's';

                const angle = (i / 8) * Math.PI * 2;
                const distance = 50;
                particle.style.setProperty('--end-x', Math.cos(angle) * distance + 'px');
                particle.style.setProperty('--end-y', Math.sin(angle) * distance + 'px');

                document.body.appendChild(particle);

                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 3000);
            }
        }

        function handleColorClick(colorId) {
            if (gameState.showingSequence || gameState.isCompleted) return;

            const currentIndex = gameState.userSequence.length;
            gameState.userSequence.push(colorId);

            // Enhanced visual feedback for user click
            const colorBtn = document.getElementById(`color-${colorId}`);
            colorBtn.style.transform = 'scale(1.15)';
            colorBtn.classList.add('ring-2', 'ring-white');

            // Create click particle effect
            createParticleEffect(colorBtn);

            setTimeout(() => {
                colorBtn.style.transform = 'scale(1)';
                colorBtn.classList.remove('ring-2', 'ring-white');
            }, 200);

            if (colorId === gameState.sequence[currentIndex]) {
                // Correct! Show positive feedback
                colorBtn.style.boxShadow = '0 0 20px rgba(34, 197, 94, 0.8)';
                setTimeout(() => colorBtn.style.boxShadow = '', 500);

                if (gameState.userSequence.length === gameState.sequence.length) {
                    // Round completed!
                    gameState.score++;
                    const level = Math.floor(gameState.score / 3) + 1;

                    document.getElementById('score').textContent = gameState.score;
                    document.getElementById('level').textContent = level;
                    document.getElementById('game-message').textContent = `🎉 Perfect! Level ${level}`;
                    document.getElementById('game-subtitle').textContent = `Score: ${gameState.score} • Keep going!`;

                    // Show celebration effect
                    showCelebrationEffect();

                    setTimeout(() => {
                        if (gameState.timeRemaining > 5) {
                            startNewRound();
                        } else {
                            endGame();
                        }
                    }, 1500);
                } else {
                    // Partial success
                    document.getElementById('game-message').textContent = `✅ Correct! ${gameState.userSequence.length}/${gameState.sequence.length}`;
                    document.getElementById('game-subtitle').textContent = 'Keep going...';
                }
            } else {
                // Wrong! Show negative feedback
                colorBtn.style.boxShadow = '0 0 20px rgba(239, 68, 68, 0.8)';
                setTimeout(() => colorBtn.style.boxShadow = '', 500);

                document.getElementById('game-message').textContent = '❌ Oops! Starting over...';
                document.getElementById('game-subtitle').textContent = 'Don\'t worry, you\'ve got this!';

                // Shake effect for wrong answer
                document.querySelector('.glass').style.animation = 'wiggle 0.5s ease-in-out';
                setTimeout(() => {
                    document.querySelector('.glass').style.animation = '';
                }, 500);

                setTimeout(() => {
                    startNewRound();
                }, 2000);
            }
        }

        function showCelebrationEffect() {
            // Create multiple celebration particles
            const gameArea = document.querySelector('.glass');
            const rect = gameArea.getBoundingClientRect();

            for (let i = 0; i < 15; i++) {
                const particle = document.createElement('div');
                particle.innerHTML = ['🎉', '✨', '🌟', '💫', '⭐'][Math.floor(Math.random() * 5)];
                particle.style.position = 'fixed';
                particle.style.left = (rect.left + Math.random() * rect.width) + 'px';
                particle.style.top = (rect.top + Math.random() * rect.height) + 'px';
                particle.style.fontSize = '20px';
                particle.style.pointerEvents = 'none';
                particle.style.zIndex = '1000';
                particle.style.animation = 'particle 2s ease-out forwards';

                document.body.appendChild(particle);

                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 2000);
            }
        }

        function endGame() {
            gameState.isActive = false;
            gameState.isCompleted = true;

            document.getElementById('game-message').textContent = `Game Over! Final Score: ${gameState.score}`;
            document.getElementById('final-score').textContent = gameState.score;

            // Disable all color buttons
            document.querySelectorAll('.color-btn').forEach(btn => {
                btn.disabled = true;
                btn.classList.add('cursor-not-allowed', 'opacity-70');
            });

            // Show completion message
            setTimeout(() => {
                document.getElementById('completion-message').classList.remove('hidden');
            }, 2000);
        }
        // Color Clash Game (Stroop Effect)
        function startColorClashGame() {
            const colorWords = ['RED', 'BLUE', 'GREEN', 'YELLOW', 'PURPLE', 'ORANGE'];
            const colors = ['text-red-500', 'text-blue-500', 'text-green-500', 'text-yellow-500', 'text-purple-500', 'text-orange-500'];
            let currentWord, currentColor, correctAnswer, score = 0, timeLeft = selectedTime * 60;

            function generateChallenge() {
                const wordIndex = Math.floor(Math.random() * colorWords.length);
                const colorIndex = Math.floor(Math.random() * colors.length);
                currentWord = colorWords[wordIndex];
                currentColor = colors[colorIndex];
                correctAnswer = colorIndex;
                return { word: currentWord, color: currentColor };
            }

            const challenge = generateChallenge();

            document.querySelector('main').innerHTML = `
                <div class="flex-1 p-6 animate-fade-in">
                    <div class="max-w-4xl mx-auto">
                        <div class="flex justify-between items-center mb-8">
                            <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                <span class="font-medium">Back Home</span>
                            </button>

                            <div class="text-center">
                                <div class="text-4xl mb-2 animate-bounce-gentle">🌈</div>
                                <h1 class="text-3xl font-bold gradient-text">Color Clash</h1>
                                <p class="text-white/60">Brain Teaser • Medium</p>
                            </div>

                            <button onclick="startActivity()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span class="font-medium">New Game</span>
                            </button>
                        </div>

                        <div class="glass rounded-3xl p-8 mb-8">
                            <div class="text-center mb-8">
                                <p class="text-xl text-white/90 mb-4">
                                    🧠 <strong>Don't get confused!</strong> Click the color the word is displayed in, not what it says.
                                </p>
                            </div>

                            <div class="grid grid-cols-3 gap-6 mb-8">
                                <div class="glass rounded-2xl p-4 text-center">
                                    <div class="text-2xl font-bold text-white" id="clash-score">0</div>
                                    <div class="text-white/60 text-sm">Score</div>
                                </div>
                                <div class="glass rounded-2xl p-4 text-center">
                                    <div class="text-2xl font-bold text-emerald-400" id="clash-timer">${Math.floor(timeLeft/60)}:${(timeLeft%60).toString().padStart(2,'0')}</div>
                                    <div class="text-white/60 text-sm">Time Left</div>
                                </div>
                                <div class="glass rounded-2xl p-4 text-center">
                                    <div class="text-2xl font-bold text-purple-400" id="clash-streak">0</div>
                                    <div class="text-white/60 text-sm">Streak</div>
                                </div>
                            </div>

                            <div class="text-center mb-8">
                                <div class="glass rounded-2xl p-8 mb-6">
                                    <p class="text-white/80 mb-4">Click the color this word is displayed in:</p>
                                    <div class="text-6xl font-bold mb-6 ${challenge.color}" id="color-word">${challenge.word}</div>
                                </div>
                            </div>

                            <div class="grid grid-cols-3 gap-4 max-w-2xl mx-auto mb-8">
                                ${colors.map((color, index) => `
                                    <button onclick="checkColorAnswer(${index})" class="glass rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 transform hover:scale-105 group">
                                        <div class="w-16 h-16 mx-auto mb-3 rounded-full ${color.replace('text-', 'bg-')} shadow-lg group-hover:shadow-xl transition-all"></div>
                                        <div class="text-white font-medium">${colorWords[index]}</div>
                                    </button>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            let streak = 0;

            // Timer
            const timer = setInterval(() => {
                timeLeft--;
                const mins = Math.floor(timeLeft / 60);
                const secs = timeLeft % 60;
                document.getElementById('clash-timer').textContent = `${mins}:${secs.toString().padStart(2, '0')}`;

                if (timeLeft <= 0) {
                    clearInterval(timer);
                    showColorClashResults();
                }
            }, 1000);

            window.checkColorAnswer = function(selectedIndex) {
                if (selectedIndex === correctAnswer) {
                    score++;
                    streak++;
                    document.getElementById('clash-score').textContent = score;
                    document.getElementById('clash-streak').textContent = streak;
                    showCelebrationEffect();
                } else {
                    streak = 0;
                    document.getElementById('clash-streak').textContent = streak;
                }
                nextColorChallenge();
            };

            window.nextColorChallenge = function() {
                const challenge = generateChallenge();
                document.getElementById('color-word').textContent = challenge.word;
                document.getElementById('color-word').className = `text-6xl font-bold mb-6 ${challenge.color}`;
            };

            function showColorClashResults() {
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 flex items-center justify-center p-8">
                        <div class="max-w-2xl mx-auto text-center animate-scale-in">
                            <div class="glass rounded-3xl p-12">
                                <div class="text-8xl mb-6">🎉</div>
                                <h2 class="text-4xl font-bold text-white mb-4 gradient-text">Brain Power!</h2>
                                <p class="text-xl text-white/80 mb-4">Final Score: <span class="text-emerald-400 font-bold">${score}</span></p>
                                <p class="text-white/60 mb-8">You've earned <span class="text-yellow-400 font-semibold">+${Math.max(10, score * 2)} coins</span>!</p>

                                <div class="space-y-4">
                                    <button onclick="startSpecificActivity('color-clash')" class="w-full bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🌈 Play Again
                                    </button>
                                    <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🎯 Try Another Activity
                                    </button>
                                    <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                        🏠 Back to Home
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // Would You Rather Game
        function startWouldYouRatherGame() {
            const questions = [
                { option1: "Have the ability to fly", option2: "Have the ability to read minds" },
                { option1: "Always be 10 minutes late", option2: "Always be 20 minutes early" },
                { option1: "Live without music", option2: "Live without movies" },
                { option1: "Have unlimited money", option2: "Have unlimited time" },
                { option1: "Be able to speak all languages", option2: "Be able to talk to animals" },
                { option1: "Never have to sleep", option2: "Never have to eat" },
                { option1: "Live in the past", option2: "Live in the future" },
                { option1: "Have super strength", option2: "Have super speed" },
                { option1: "Be famous but poor", option2: "Be rich but unknown" },
                { option1: "Always tell the truth", option2: "Always lie" }
            ];

            let currentQuestion = 0;
            let answers = [];

            function showQuestion() {
                const q = questions[currentQuestion];
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 p-6 animate-fade-in">
                        <div class="max-w-4xl mx-auto">
                            <div class="flex justify-between items-center mb-8">
                                <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                    <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                    </svg>
                                    <span class="font-medium">Back Home</span>
                                </button>

                                <div class="text-center">
                                    <div class="text-4xl mb-2 animate-bounce-gentle">🤔</div>
                                    <h1 class="text-3xl font-bold gradient-text">Would You Rather?</h1>
                                    <p class="text-white/60">Question ${currentQuestion + 1} of ${questions.length}</p>
                                </div>

                                <button onclick="startActivity()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                    <svg class="w-5 h-5 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    <span class="font-medium">New Questions</span>
                                </button>
                            </div>

                            <div class="glass rounded-3xl p-8 mb-8">
                                <div class="text-center mb-8">
                                    <h2 class="text-3xl font-bold text-white mb-6">Which would you choose?</h2>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
                                    <button onclick="selectOption(1)" class="glass rounded-2xl p-8 hover:bg-gradient-to-br hover:from-pink-500/20 hover:to-violet-500/20 transition-all duration-300 transform hover:scale-105 group">
                                        <div class="text-6xl mb-4 group-hover:animate-bounce-gentle">A</div>
                                        <p class="text-xl text-white font-semibold">${q.option1}</p>
                                    </button>

                                    <button onclick="selectOption(2)" class="glass rounded-2xl p-8 hover:bg-gradient-to-br hover:from-blue-500/20 hover:to-cyan-500/20 transition-all duration-300 transform hover:scale-105 group">
                                        <div class="text-6xl mb-4 group-hover:animate-bounce-gentle">B</div>
                                        <p class="text-xl text-white font-semibold">${q.option2}</p>
                                    </button>
                                </div>

                                <div class="text-center mt-8">
                                    <div class="w-full bg-white/20 rounded-full h-2 mb-4">
                                        <div class="bg-gradient-to-r from-pink-500 to-violet-500 h-2 rounded-full transition-all duration-500" style="width: ${((currentQuestion) / questions.length) * 100}%"></div>
                                    </div>
                                    <p class="text-white/60">Progress: ${currentQuestion}/${questions.length}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            window.selectOption = function(option) {
                answers.push(option);
                currentQuestion++;

                if (currentQuestion < questions.length) {
                    showQuestion();
                } else {
                    showWouldYouRatherResults();
                }
            };

            function showWouldYouRatherResults() {
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 flex items-center justify-center p-8">
                        <div class="max-w-2xl mx-auto text-center animate-scale-in">
                            <div class="glass rounded-3xl p-12">
                                <div class="text-8xl mb-6">🎭</div>
                                <h2 class="text-4xl font-bold text-white mb-4 gradient-text">Choices Revealed!</h2>
                                <p class="text-xl text-white/80 mb-4">You completed ${questions.length} tough decisions!</p>
                                <p class="text-white/60 mb-8">You've earned <span class="text-yellow-400 font-semibold">+20 coins</span>!</p>

                                <div class="space-y-4">
                                    <button onclick="startSpecificActivity('would-you-rather')" class="w-full bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🤔 More Questions
                                    </button>
                                    <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🎯 Try Another Activity
                                    </button>
                                    <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                        🏠 Back to Home
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            showQuestion();
        }

        // Emoji Decode Game
        function startEmojiDecodeGame() {
            const puzzles = [
                { emojis: "🍎📱", answer: "APPLE", hint: "Tech company" },
                { emojis: "⭐🔥", answer: "STARFIRE", hint: "Superhero name" },
                { emojis: "🌙🚶‍♂️", answer: "MOONWALK", hint: "Famous dance move" },
                { emojis: "🐝🎬", answer: "BEE MOVIE", hint: "Animated film" },
                { emojis: "🏠🔥", answer: "HOUSE FIRE", hint: "Emergency situation" },
                { emojis: "🎵🎤", answer: "MUSIC", hint: "Sound art" },
                { emojis: "🌈🦄", answer: "RAINBOW UNICORN", hint: "Magical creature" },
                { emojis: "🍕❤️", answer: "PIZZA LOVE", hint: "Food passion" },
                { emojis: "🚗💨", answer: "FAST CAR", hint: "Speed vehicle" },
                { emojis: "📚🐛", answer: "BOOKWORM", hint: "Reading enthusiast" }
            ];

            let currentPuzzle = 0;
            let score = 0;
            let timeLeft = selectedTime * 60;

            function showPuzzle() {
                const puzzle = puzzles[currentPuzzle];
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 p-6 animate-fade-in">
                        <div class="max-w-4xl mx-auto">
                            <div class="flex justify-between items-center mb-8">
                                <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                    <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                    </svg>
                                    <span class="font-medium">Back Home</span>
                                </button>

                                <div class="text-center">
                                    <div class="text-4xl mb-2 animate-bounce-gentle">😎</div>
                                    <h1 class="text-3xl font-bold gradient-text">Emoji Decode</h1>
                                    <p class="text-white/60">Puzzle ${currentPuzzle + 1} of ${puzzles.length}</p>
                                </div>

                                <button onclick="startActivity()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                    <svg class="w-5 h-5 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    <span class="font-medium">New Puzzle</span>
                                </button>
                            </div>

                            <div class="glass rounded-3xl p-8 mb-8">
                                <div class="grid grid-cols-3 gap-6 mb-8">
                                    <div class="glass rounded-2xl p-4 text-center">
                                        <div class="text-2xl font-bold text-white" id="emoji-score">${score}</div>
                                        <div class="text-white/60 text-sm">Score</div>
                                    </div>
                                    <div class="glass rounded-2xl p-4 text-center">
                                        <div class="text-2xl font-bold text-emerald-400" id="emoji-timer">${Math.floor(timeLeft/60)}:${(timeLeft%60).toString().padStart(2,'0')}</div>
                                        <div class="text-white/60 text-sm">Time Left</div>
                                    </div>
                                    <div class="glass rounded-2xl p-4 text-center">
                                        <div class="text-2xl font-bold text-purple-400">${currentPuzzle + 1}/${puzzles.length}</div>
                                        <div class="text-white/60 text-sm">Progress</div>
                                    </div>
                                </div>

                                <div class="text-center mb-8">
                                    <h2 class="text-2xl font-bold text-white mb-6">Decode this emoji puzzle:</h2>
                                    <div class="text-8xl mb-6 animate-bounce-gentle">${puzzle.emojis}</div>
                                    <p class="text-lg text-white/70 mb-6">Hint: ${puzzle.hint}</p>

                                    <input type="text" id="emoji-answer" placeholder="Type your answer..." class="w-full max-w-md mx-auto bg-white/10 border border-white/20 rounded-2xl px-6 py-4 text-white text-center text-xl placeholder-white/50 focus:outline-none focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 mb-6">

                                    <div class="space-y-4">
                                        <button onclick="checkEmojiAnswer()" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-12 rounded-2xl text-xl shadow-2xl transform hover:scale-105 transition-all duration-300">
                                            Submit Answer
                                        </button>
                                        <button onclick="skipEmojiPuzzle()" class="glass hover:bg-white/10 text-white font-medium py-3 px-8 rounded-2xl transition-all">
                                            Skip This One
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Focus on input
                setTimeout(() => {
                    document.getElementById('emoji-answer').focus();
                }, 100);

                // Enter key support
                document.getElementById('emoji-answer').addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        checkEmojiAnswer();
                    }
                });
            }

            // Timer
            const timer = setInterval(() => {
                timeLeft--;
                const mins = Math.floor(timeLeft / 60);
                const secs = timeLeft % 60;
                if (document.getElementById('emoji-timer')) {
                    document.getElementById('emoji-timer').textContent = `${mins}:${secs.toString().padStart(2, '0')}`;
                }

                if (timeLeft <= 0) {
                    clearInterval(timer);
                    showEmojiResults();
                }
            }, 1000);

            window.checkEmojiAnswer = function() {
                const userAnswer = document.getElementById('emoji-answer').value.toUpperCase().trim();
                const correctAnswer = puzzles[currentPuzzle].answer.toUpperCase();

                if (userAnswer === correctAnswer) {
                    score++;
                    showCelebrationEffect();
                }

                currentPuzzle++;
                if (currentPuzzle < puzzles.length && timeLeft > 10) {
                    showPuzzle();
                } else {
                    clearInterval(timer);
                    showEmojiResults();
                }
            };

            window.skipEmojiPuzzle = function() {
                currentPuzzle++;
                if (currentPuzzle < puzzles.length && timeLeft > 10) {
                    showPuzzle();
                } else {
                    clearInterval(timer);
                    showEmojiResults();
                }
            };

            function showEmojiResults() {
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 flex items-center justify-center p-8">
                        <div class="max-w-2xl mx-auto text-center animate-scale-in">
                            <div class="glass rounded-3xl p-12">
                                <div class="text-8xl mb-6">🎉</div>
                                <h2 class="text-4xl font-bold text-white mb-4 gradient-text">Emoji Master!</h2>
                                <p class="text-xl text-white/80 mb-4">You solved <span class="text-emerald-400 font-bold">${score}</span> out of ${currentPuzzle} puzzles!</p>
                                <p class="text-white/60 mb-8">You've earned <span class="text-yellow-400 font-semibold">+${Math.max(15, score * 3)} coins</span>!</p>

                                <div class="space-y-4">
                                    <button onclick="startSpecificActivity('emoji-decode')" class="w-full bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        😎 More Puzzles
                                    </button>
                                    <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🎯 Try Another Activity
                                    </button>
                                    <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                        🏠 Back to Home
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            showPuzzle();
        }

        // Reaction Spark Game
        function startReactionSparkGame() {
            let score = 0;
            let timeLeft = selectedTime * 60;
            let gameActive = false;
            let reactionStart = 0;
            let bestTime = Infinity;

            function showReactionGame() {
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 p-6 animate-fade-in">
                        <div class="max-w-4xl mx-auto">
                            <div class="flex justify-between items-center mb-8">
                                <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                    <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                    </svg>
                                    <span class="font-medium">Back Home</span>
                                </button>

                                <div class="text-center">
                                    <div class="text-4xl mb-2 animate-bounce-gentle">⚡</div>
                                    <h1 class="text-3xl font-bold gradient-text">Reaction Spark</h1>
                                    <p class="text-white/60">Speed Game • Medium</p>
                                </div>

                                <button onclick="startActivity()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                    <svg class="w-5 h-5 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    <span class="font-medium">New Game</span>
                                </button>
                            </div>

                            <div class="glass rounded-3xl p-8 mb-8">
                                <div class="grid grid-cols-3 gap-6 mb-8">
                                    <div class="glass rounded-2xl p-4 text-center">
                                        <div class="text-2xl font-bold text-white" id="reaction-score">${score}</div>
                                        <div class="text-white/60 text-sm">Score</div>
                                    </div>
                                    <div class="glass rounded-2xl p-4 text-center">
                                        <div class="text-2xl font-bold text-emerald-400" id="reaction-timer">${Math.floor(timeLeft/60)}:${(timeLeft%60).toString().padStart(2,'0')}</div>
                                        <div class="text-white/60 text-sm">Time Left</div>
                                    </div>
                                    <div class="glass rounded-2xl p-4 text-center">
                                        <div class="text-2xl font-bold text-purple-400" id="best-time">${bestTime === Infinity ? '--' : bestTime + 'ms'}</div>
                                        <div class="text-white/60 text-sm">Best Time</div>
                                    </div>
                                </div>

                                <div class="text-center mb-8">
                                    <div id="reaction-area" class="w-80 h-80 mx-auto rounded-3xl glass flex items-center justify-center cursor-pointer transition-all duration-300 bg-red-500/20" onclick="handleReactionClick()">
                                        <div id="reaction-message" class="text-2xl font-bold text-white">
                                            Click when it turns GREEN!
                                        </div>
                                    </div>

                                    <div class="mt-6">
                                        <button onclick="startReactionRound()" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-12 rounded-2xl text-xl shadow-2xl transform hover:scale-105 transition-all duration-300">
                                            Start Round
                                        </button>
                                    </div>
                                </div>

                                <div class="text-center text-white/60 text-sm">
                                    <p>Wait for the area to turn green, then click as fast as you can!</p>
                                    <p>Don't click too early or you'll have to start over.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Timer
            const timer = setInterval(() => {
                timeLeft--;
                const mins = Math.floor(timeLeft / 60);
                const secs = timeLeft % 60;
                if (document.getElementById('reaction-timer')) {
                    document.getElementById('reaction-timer').textContent = `${mins}:${secs.toString().padStart(2, '0')}`;
                }

                if (timeLeft <= 0) {
                    clearInterval(timer);
                    showReactionResults();
                }
            }, 1000);

            window.startReactionRound = function() {
                const area = document.getElementById('reaction-area');
                const message = document.getElementById('reaction-message');

                area.className = 'w-80 h-80 mx-auto rounded-3xl glass flex items-center justify-center cursor-pointer transition-all duration-300 bg-red-500/20';
                message.textContent = 'Wait for GREEN...';
                gameActive = false;

                // Random delay between 1-5 seconds
                const delay = Math.random() * 4000 + 1000;

                setTimeout(() => {
                    if (timeLeft > 0) {
                        area.className = 'w-80 h-80 mx-auto rounded-3xl glass flex items-center justify-center cursor-pointer transition-all duration-300 bg-green-500/50 animate-pulse';
                        message.textContent = 'CLICK NOW!';
                        gameActive = true;
                        reactionStart = Date.now();
                    }
                }, delay);
            };

            window.handleReactionClick = function() {
                const area = document.getElementById('reaction-area');
                const message = document.getElementById('reaction-message');

                if (!gameActive) {
                    // Clicked too early
                    area.className = 'w-80 h-80 mx-auto rounded-3xl glass flex items-center justify-center cursor-pointer transition-all duration-300 bg-red-500/50';
                    message.textContent = 'Too early! Try again.';
                    return;
                }

                // Calculate reaction time
                const reactionTime = Date.now() - reactionStart;
                score++;

                if (reactionTime < bestTime) {
                    bestTime = reactionTime;
                    document.getElementById('best-time').textContent = bestTime + 'ms';
                }

                document.getElementById('reaction-score').textContent = score;

                area.className = 'w-80 h-80 mx-auto rounded-3xl glass flex items-center justify-center cursor-pointer transition-all duration-300 bg-blue-500/50';
                message.textContent = `${reactionTime}ms! Great reflexes!`;

                gameActive = false;

                // Show celebration
                showCelebrationEffect();

                // Auto start next round after 2 seconds
                setTimeout(() => {
                    if (timeLeft > 5) {
                        startReactionRound();
                    }
                }, 2000);
            };

            function showReactionResults() {
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 flex items-center justify-center p-8">
                        <div class="max-w-2xl mx-auto text-center animate-scale-in">
                            <div class="glass rounded-3xl p-12">
                                <div class="text-8xl mb-6">⚡</div>
                                <h2 class="text-4xl font-bold text-white mb-4 gradient-text">Lightning Fast!</h2>
                                <p class="text-xl text-white/80 mb-2">Successful reactions: <span class="text-emerald-400 font-bold">${score}</span></p>
                                <p class="text-xl text-white/80 mb-4">Best time: <span class="text-purple-400 font-bold">${bestTime === Infinity ? 'N/A' : bestTime + 'ms'}</span></p>
                                <p class="text-white/60 mb-8">You've earned <span class="text-yellow-400 font-semibold">+${Math.max(12, score * 2)} coins</span>!</p>

                                <div class="space-y-4">
                                    <button onclick="startSpecificActivity('reaction-spark')" class="w-full bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        ⚡ Test Again
                                    </button>
                                    <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🎯 Try Another Activity
                                    </button>
                                    <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                        🏠 Back to Home
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            showReactionGame();
        }

        // Box Breathing Activity
        function startBoxBreathingActivity() {
            let phase = 'inhale';
            let cycleCount = 0;
            let timeLeft = selectedTime * 60;
            let isActive = false;

            document.querySelector('main').innerHTML = `
                <div class="flex-1 p-6 animate-fade-in">
                    <div class="max-w-4xl mx-auto">
                        <div class="flex justify-between items-center mb-8">
                            <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                <span class="font-medium">Back Home</span>
                            </button>

                            <div class="text-center">
                                <div class="text-4xl mb-2 animate-bounce-gentle">🫁</div>
                                <h1 class="text-3xl font-bold gradient-text">Box Breathing</h1>
                                <p class="text-white/60">Breathing • Easy</p>
                            </div>

                            <button onclick="startActivity()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span class="font-medium">New Session</span>
                            </button>
                        </div>

                        <div class="glass rounded-3xl p-8 mb-8">
                            <div class="grid grid-cols-3 gap-6 mb-8">
                                <div class="glass rounded-2xl p-4 text-center">
                                    <div class="text-2xl font-bold text-white" id="breath-cycles">${cycleCount}</div>
                                    <div class="text-white/60 text-sm">Cycles</div>
                                </div>
                                <div class="glass rounded-2xl p-4 text-center">
                                    <div class="text-2xl font-bold text-emerald-400" id="breath-timer">${Math.floor(timeLeft/60)}:${(timeLeft%60).toString().padStart(2,'0')}</div>
                                    <div class="text-white/60 text-sm">Time Left</div>
                                </div>
                                <div class="glass rounded-2xl p-4 text-center">
                                    <div class="text-2xl font-bold text-purple-400" id="breath-phase">Ready</div>
                                    <div class="text-white/60 text-sm">Phase</div>
                                </div>
                            </div>

                            <div class="text-center mb-8">
                                <div class="relative">
                                    <div id="breathing-square" class="w-64 h-64 mx-auto border-4 border-blue-400 rounded-2xl transition-all duration-4000 ease-in-out flex items-center justify-center">
                                        <div class="text-2xl font-bold text-white" id="breath-instruction">Click Start to Begin</div>
                                    </div>
                                </div>

                                <div class="mt-8">
                                    <button onclick="startBreathing()" id="breath-start-btn" class="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-bold py-4 px-12 rounded-2xl text-xl shadow-2xl transform hover:scale-105 transition-all duration-300">
                                        Start Breathing
                                    </button>
                                    <button onclick="stopBreathing()" id="breath-stop-btn" class="hidden glass hover:bg-white/10 text-white font-medium py-3 px-8 rounded-2xl transition-all ml-4">
                                        Finish Early
                                    </button>
                                </div>
                            </div>

                            <div class="text-center text-white/60 text-sm">
                                <p>Follow the square: Inhale (4s) → Hold (4s) → Exhale (4s) → Hold (4s)</p>
                                <p>This technique helps reduce stress and improve focus.</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            let breathingInterval;
            let phaseTimer = 0;

            window.startBreathing = function() {
                isActive = true;
                document.getElementById('breath-start-btn').classList.add('hidden');
                document.getElementById('breath-stop-btn').classList.remove('hidden');

                breathingInterval = setInterval(() => {
                    phaseTimer++;

                    if (phaseTimer <= 4) {
                        phase = 'inhale';
                        document.getElementById('breath-phase').textContent = 'Inhale';
                        document.getElementById('breath-instruction').textContent = 'Breathe In';
                        document.getElementById('breathing-square').style.transform = 'scale(1.3)';
                        document.getElementById('breathing-square').style.borderColor = '#3b82f6';
                    } else if (phaseTimer <= 8) {
                        phase = 'hold1';
                        document.getElementById('breath-phase').textContent = 'Hold';
                        document.getElementById('breath-instruction').textContent = 'Hold';
                        document.getElementById('breathing-square').style.borderColor = '#8b5cf6';
                    } else if (phaseTimer <= 12) {
                        phase = 'exhale';
                        document.getElementById('breath-phase').textContent = 'Exhale';
                        document.getElementById('breath-instruction').textContent = 'Breathe Out';
                        document.getElementById('breathing-square').style.transform = 'scale(1)';
                        document.getElementById('breathing-square').style.borderColor = '#10b981';
                    } else if (phaseTimer <= 16) {
                        phase = 'hold2';
                        document.getElementById('breath-phase').textContent = 'Hold';
                        document.getElementById('breath-instruction').textContent = 'Hold';
                        document.getElementById('breathing-square').style.borderColor = '#f59e0b';
                    } else {
                        phaseTimer = 0;
                        cycleCount++;
                        document.getElementById('breath-cycles').textContent = cycleCount;
                    }
                }, 1000);
            };

            window.stopBreathing = function() {
                clearInterval(breathingInterval);
                showBreathingResults();
            };

            // Timer
            const timer = setInterval(() => {
                timeLeft--;
                const mins = Math.floor(timeLeft / 60);
                const secs = timeLeft % 60;
                if (document.getElementById('breath-timer')) {
                    document.getElementById('breath-timer').textContent = `${mins}:${secs.toString().padStart(2, '0')}`;
                }

                if (timeLeft <= 0) {
                    clearInterval(timer);
                    clearInterval(breathingInterval);
                    showBreathingResults();
                }
            }, 1000);

            function showBreathingResults() {
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 flex items-center justify-center p-8">
                        <div class="max-w-2xl mx-auto text-center animate-scale-in">
                            <div class="glass rounded-3xl p-12">
                                <div class="text-8xl mb-6">🧘</div>
                                <h2 class="text-4xl font-bold text-white mb-4 gradient-text">Peaceful Mind</h2>
                                <p class="text-xl text-white/80 mb-4">You completed <span class="text-blue-400 font-bold">${cycleCount}</span> breathing cycles!</p>
                                <p class="text-white/60 mb-8">You've earned <span class="text-yellow-400 font-semibold">+18 coins</span>!</p>

                                <div class="space-y-4">
                                    <button onclick="startSpecificActivity('box-breathing')" class="w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🫁 Breathe Again
                                    </button>
                                    <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🎯 Try Another Activity
                                    </button>
                                    <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                        🏠 Back to Home
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // Gratitude Drop Activity
        function startGratitudeDropActivity() {
            let gratitudeItems = [];
            let timeLeft = selectedTime * 60;

            document.querySelector('main').innerHTML = `
                <div class="flex-1 p-6 animate-fade-in">
                    <div class="max-w-4xl mx-auto">
                        <div class="flex justify-between items-center mb-8">
                            <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                <span class="font-medium">Back Home</span>
                            </button>

                            <div class="text-center">
                                <div class="text-4xl mb-2 animate-bounce-gentle">💖</div>
                                <h1 class="text-3xl font-bold gradient-text">Gratitude Drop</h1>
                                <p class="text-white/60">Reflection • Easy</p>
                            </div>

                            <button onclick="startActivity()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span class="font-medium">New Session</span>
                            </button>
                        </div>

                        <div class="glass rounded-3xl p-8 mb-8">
                            <div class="text-center mb-8">
                                <h2 class="text-2xl font-bold text-white mb-4">What are you grateful for today?</h2>
                                <p class="text-white/70">Take a moment to appreciate the good things in your life</p>
                            </div>

                            <div class="max-w-2xl mx-auto">
                                <div class="glass rounded-2xl p-6 mb-6">
                                    <textarea id="gratitude-input" placeholder="I'm grateful for..." class="w-full h-32 bg-transparent border-none text-white text-lg placeholder-white/50 resize-none focus:outline-none"></textarea>
                                    <div class="flex justify-between items-center mt-4">
                                        <span class="text-white/50 text-sm" id="char-count">0/200 characters</span>
                                        <button onclick="addGratitude()" class="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white font-bold py-2 px-6 rounded-lg transition-all">
                                            Add ❤️
                                        </button>
                                    </div>
                                </div>

                                <div id="gratitude-list" class="space-y-3 mb-6">
                                    <!-- Gratitude items will appear here -->
                                </div>

                                <div class="text-center">
                                    <div class="text-white/60 mb-4">Progress: <span id="gratitude-count">0</span>/3 items</div>
                                    <div class="w-full bg-white/20 rounded-full h-2 mb-6">
                                        <div id="gratitude-progress" class="bg-gradient-to-r from-pink-500 to-rose-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                                    </div>

                                    <button onclick="finishGratitude()" id="finish-btn" class="hidden bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-12 rounded-2xl text-xl shadow-2xl transform hover:scale-105 transition-all duration-300">
                                        Complete Activity
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Character counter
            document.getElementById('gratitude-input').addEventListener('input', function() {
                const count = this.value.length;
                document.getElementById('char-count').textContent = `${count}/200 characters`;
                if (count > 200) {
                    this.value = this.value.substring(0, 200);
                }
            });

            window.addGratitude = function() {
                const input = document.getElementById('gratitude-input');
                const text = input.value.trim();

                if (text && gratitudeItems.length < 5) {
                    gratitudeItems.push(text);
                    input.value = '';
                    document.getElementById('char-count').textContent = '0/200 characters';
                    updateGratitudeDisplay();
                }
            };

            function updateGratitudeDisplay() {
                const list = document.getElementById('gratitude-list');
                list.innerHTML = gratitudeItems.map((item, index) => `
                    <div class="glass rounded-lg p-4 animate-slide-up">
                        <div class="flex items-start gap-3">
                            <div class="text-2xl">💖</div>
                            <p class="text-white/90 flex-1">${item}</p>
                            <button onclick="removeGratitude(${index})" class="text-white/40 hover:text-white/80 transition-colors">×</button>
                        </div>
                    </div>
                `).join('');

                document.getElementById('gratitude-count').textContent = gratitudeItems.length;
                document.getElementById('gratitude-progress').style.width = `${(gratitudeItems.length / 3) * 100}%`;

                if (gratitudeItems.length >= 3) {
                    document.getElementById('finish-btn').classList.remove('hidden');
                }
            }

            window.removeGratitude = function(index) {
                gratitudeItems.splice(index, 1);
                updateGratitudeDisplay();
            };

            window.finishGratitude = function() {
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 flex items-center justify-center p-8">
                        <div class="max-w-2xl mx-auto text-center animate-scale-in">
                            <div class="glass rounded-3xl p-12">
                                <div class="text-8xl mb-6">💖</div>
                                <h2 class="text-4xl font-bold text-white mb-4 gradient-text">Beautiful Gratitude!</h2>
                                <p class="text-xl text-white/80 mb-4">You shared <span class="text-pink-400 font-bold">${gratitudeItems.length}</span> moments of gratitude!</p>
                                <p class="text-white/60 mb-8">You've earned <span class="text-yellow-400 font-semibold">+22 coins</span>!</p>

                                <div class="space-y-4">
                                    <button onclick="startSpecificActivity('gratitude-drop')" class="w-full bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        💖 More Gratitude
                                    </button>
                                    <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🎯 Try Another Activity
                                    </button>
                                    <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                        🏠 Back to Home
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            };
        }

        // Micro Learning Activity
        function startMicroLearningActivity() {
            const facts = [
                {
                    title: "Why Do We Dream?",
                    content: "Dreams occur during REM sleep when our brain processes emotions and consolidates memories. Scientists believe dreams help us rehearse scenarios and strengthen neural pathways. Interestingly, we forget about 95% of our dreams within minutes of waking up!",
                    category: "Neuroscience"
                },
                {
                    title: "The Speed of Lightning",
                    content: "Lightning travels at about 1/3 the speed of light (100,000 km/s), while thunder travels at the speed of sound (343 m/s). This is why you see lightning before hearing thunder. You can estimate distance by counting seconds between flash and thunder, then dividing by 5 for miles!",
                    category: "Physics"
                },
                {
                    title: "Octopus Intelligence",
                    content: "Octopuses have three hearts, blue blood, and incredible intelligence! They can solve puzzles, use tools, and show personality traits. Each arm has its own 'brain' that can taste and smell independently. They're masters of camouflage, changing color and texture in milliseconds.",
                    category: "Biology"
                }
            ];

            const randomFact = facts[Math.floor(Math.random() * facts.length)];

            document.querySelector('main').innerHTML = `
                <div class="flex-1 p-6 animate-fade-in">
                    <div class="max-w-4xl mx-auto">
                        <div class="flex justify-between items-center mb-8">
                            <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                <span class="font-medium">Back Home</span>
                            </button>

                            <div class="text-center">
                                <div class="text-4xl mb-2 animate-bounce-gentle">🧠</div>
                                <h1 class="text-3xl font-bold gradient-text">60-Second Science</h1>
                                <p class="text-white/60">Learning • Easy</p>
                            </div>

                            <button onclick="startSpecificActivity('micro-learning')" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span class="font-medium">New Fact</span>
                            </button>
                        </div>

                        <div class="glass rounded-3xl p-8 mb-8">
                            <div class="text-center mb-8">
                                <div class="inline-block bg-gradient-to-r from-pink-500 to-violet-500 text-white text-sm font-semibold px-4 py-2 rounded-full mb-4">
                                    ${randomFact.category}
                                </div>
                                <h2 class="text-3xl font-bold text-white mb-6">${randomFact.title}</h2>
                            </div>

                            <div class="max-w-3xl mx-auto">
                                <div class="glass rounded-2xl p-8 mb-8">
                                    <p class="text-lg text-white/90 leading-relaxed">${randomFact.content}</p>
                                </div>

                                <div class="text-center space-y-4">
                                    <div class="text-white/60 mb-6">
                                        <p>🧠 <strong>Did you know?</strong> Learning new facts creates new neural pathways!</p>
                                    </div>

                                    <button onclick="finishLearning()" class="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-12 rounded-2xl text-xl shadow-2xl transform hover:scale-105 transition-all duration-300">
                                        Knowledge Gained! ✨
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            window.finishLearning = function() {
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 flex items-center justify-center p-8">
                        <div class="max-w-2xl mx-auto text-center animate-scale-in">
                            <div class="glass rounded-3xl p-12">
                                <div class="text-8xl mb-6">🧠</div>
                                <h2 class="text-4xl font-bold text-white mb-4 gradient-text">Knowledge Unlocked!</h2>
                                <p class="text-xl text-white/80 mb-4">You've expanded your mind with fascinating science!</p>
                                <p class="text-white/60 mb-8">You've earned <span class="text-yellow-400 font-semibold">+15 coins</span>!</p>

                                <div class="space-y-4">
                                    <button onclick="startSpecificActivity('micro-learning')" class="w-full bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🧠 Learn More
                                    </button>
                                    <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🎯 Try Another Activity
                                    </button>
                                    <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                        🏠 Back to Home
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            };
        }

        // Trivia Blast Game
        function startTriviaBlastGame() {
            const questions = [
                { q: "What's the largest planet in our solar system?", a: ["Jupiter", "Saturn", "Earth", "Mars"], correct: 0 },
                { q: "Which element has the chemical symbol 'O'?", a: ["Gold", "Oxygen", "Silver", "Iron"], correct: 1 },
                { q: "What year did the Titanic sink?", a: ["1910", "1911", "1912", "1913"], correct: 2 },
                { q: "Which country invented pizza?", a: ["France", "Greece", "Spain", "Italy"], correct: 3 },
                { q: "What's the fastest land animal?", a: ["Cheetah", "Lion", "Horse", "Gazelle"], correct: 0 },
                { q: "How many hearts does an octopus have?", a: ["1", "2", "3", "4"], correct: 2 },
                { q: "What's the smallest country in the world?", a: ["Monaco", "Vatican City", "San Marino", "Liechtenstein"], correct: 1 },
                { q: "Which planet is known as the Red Planet?", a: ["Venus", "Jupiter", "Mars", "Saturn"], correct: 2 },
                { q: "What's the hardest natural substance?", a: ["Gold", "Iron", "Diamond", "Platinum"], correct: 2 },
                { q: "How many bones are in an adult human body?", a: ["206", "208", "210", "212"], correct: 0 }
            ];

            let currentQ = 0, score = 0, timeLeft = selectedTime * 60;

            function showQuestion() {
                const q = questions[currentQ];
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 p-6 animate-fade-in">
                        <div class="max-w-4xl mx-auto">
                            <div class="flex justify-between items-center mb-8">
                                <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                    <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                    </svg>
                                    <span class="font-medium">Back Home</span>
                                </button>

                                <div class="text-center">
                                    <div class="text-4xl mb-2 animate-bounce-gentle">🧠</div>
                                    <h1 class="text-3xl font-bold gradient-text">Trivia Blast</h1>
                                    <p class="text-white/60">Question ${currentQ + 1} of ${questions.length}</p>
                                </div>

                                <div class="text-white/80">Score: ${score}</div>
                            </div>

                            <div class="glass rounded-3xl p-8 mb-8">
                                <div class="text-center mb-8">
                                    <h2 class="text-2xl font-bold text-white mb-8">${q.q}</h2>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
                                        ${q.a.map((answer, index) => `
                                            <button onclick="answerTrivia(${index})" class="glass rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 transform hover:scale-105 group">
                                                <div class="text-lg text-white font-medium">${answer}</div>
                                            </button>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            window.answerTrivia = function(selected) {
                if (selected === questions[currentQ].correct) {
                    score++;
                    showCelebrationEffect();
                }
                currentQ++;
                if (currentQ < questions.length) {
                    setTimeout(showQuestion, 1000);
                } else {
                    showTriviaResults();
                }
            };

            function showTriviaResults() {
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 flex items-center justify-center p-8">
                        <div class="max-w-2xl mx-auto text-center animate-scale-in">
                            <div class="glass rounded-3xl p-12">
                                <div class="text-8xl mb-6">🧠</div>
                                <h2 class="text-4xl font-bold text-white mb-4 gradient-text">Trivia Master!</h2>
                                <p class="text-xl text-white/80 mb-4">You got <span class="text-emerald-400 font-bold">${score}</span> out of ${questions.length} correct!</p>
                                <p class="text-white/60 mb-8">You've earned <span class="text-yellow-400 font-semibold">+${Math.max(10, score * 3)} coins</span>!</p>

                                <div class="space-y-4">
                                    <button onclick="startSpecificActivity('trivia-blast')" class="w-full bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🧠 More Trivia
                                    </button>
                                    <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🎯 Try Another Activity
                                    </button>
                                    <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                        🏠 Back to Home
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            showQuestion();
        }

        // Word Scramble Game
        function startWordScrambleGame() {
            const words = [
                { word: "RAINBOW", scrambled: "WORBAIN", hint: "Colorful arc in the sky" },
                { word: "ELEPHANT", scrambled: "PHELETNA", hint: "Large gray animal with trunk" },
                { word: "COMPUTER", scrambled: "TUPMOCRE", hint: "Electronic device for work" },
                { word: "BUTTERFLY", scrambled: "FLYTERTUB", hint: "Colorful flying insect" },
                { word: "MOUNTAIN", scrambled: "TAINMOUN", hint: "Tall natural elevation" },
                { word: "CHOCOLATE", scrambled: "TALOCHECO", hint: "Sweet brown treat" },
                { word: "TELEPHONE", scrambled: "HONETEPLE", hint: "Device for calling people" },
                { word: "ADVENTURE", scrambled: "VENTUREAD", hint: "Exciting journey or experience" }
            ];

            let currentWord = 0, score = 0, timeLeft = selectedTime * 60;

            function showWord() {
                const w = words[currentWord];
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 p-6 animate-fade-in">
                        <div class="max-w-4xl mx-auto">
                            <div class="flex justify-between items-center mb-8">
                                <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                    <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                    </svg>
                                    <span class="font-medium">Back Home</span>
                                </button>

                                <div class="text-center">
                                    <div class="text-4xl mb-2 animate-bounce-gentle">🔤</div>
                                    <h1 class="text-3xl font-bold gradient-text">Word Scramble</h1>
                                    <p class="text-white/60">Word ${currentWord + 1} of ${words.length}</p>
                                </div>

                                <div class="text-white/80">Score: ${score}</div>
                            </div>

                            <div class="glass rounded-3xl p-8 mb-8">
                                <div class="text-center mb-8">
                                    <h2 class="text-2xl font-bold text-white mb-4">Unscramble this word:</h2>
                                    <div class="text-6xl font-bold text-white mb-4 tracking-wider">${w.scrambled}</div>
                                    <p class="text-lg text-white/70 mb-6">Hint: ${w.hint}</p>

                                    <input type="text" id="word-answer" placeholder="Type your answer..." class="w-full max-w-md mx-auto bg-white/10 border border-white/20 rounded-2xl px-6 py-4 text-white text-center text-xl placeholder-white/50 focus:outline-none focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 mb-6">

                                    <div class="space-y-4">
                                        <button onclick="checkWordAnswer()" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-12 rounded-2xl text-xl shadow-2xl transform hover:scale-105 transition-all duration-300">
                                            Submit Answer
                                        </button>
                                        <button onclick="skipWord()" class="glass hover:bg-white/10 text-white font-medium py-3 px-8 rounded-2xl transition-all">
                                            Skip This Word
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                setTimeout(() => document.getElementById('word-answer').focus(), 100);

                document.getElementById('word-answer').addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') checkWordAnswer();
                });
            }

            window.checkWordAnswer = function() {
                const userAnswer = document.getElementById('word-answer').value.toUpperCase().trim();
                if (userAnswer === words[currentWord].word) {
                    score++;
                    showCelebrationEffect();
                }
                nextWord();
            };

            window.skipWord = function() {
                nextWord();
            };

            function nextWord() {
                currentWord++;
                if (currentWord < words.length) {
                    setTimeout(showWord, 1000);
                } else {
                    showWordResults();
                }
            }

            function showWordResults() {
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 flex items-center justify-center p-8">
                        <div class="max-w-2xl mx-auto text-center animate-scale-in">
                            <div class="glass rounded-3xl p-12">
                                <div class="text-8xl mb-6">🔤</div>
                                <h2 class="text-4xl font-bold text-white mb-4 gradient-text">Word Master!</h2>
                                <p class="text-xl text-white/80 mb-4">You unscrambled <span class="text-emerald-400 font-bold">${score}</span> out of ${words.length} words!</p>
                                <p class="text-white/60 mb-8">You've earned <span class="text-yellow-400 font-semibold">+${Math.max(12, score * 4)} coins</span>!</p>

                                <div class="space-y-4">
                                    <button onclick="startSpecificActivity('word-scramble')" class="w-full bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🔤 More Words
                                    </button>
                                    <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🎯 Try Another Activity
                                    </button>
                                    <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                        🏠 Back to Home
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            showWord();
        }

        // Number Ninja Game
        function startNumberNinjaGame() {
            let score = 0, timeLeft = selectedTime * 60, currentProblem = 0;

            function generateProblem() {
                const operations = ['+', '-', '×', '÷'];
                const op = operations[Math.floor(Math.random() * operations.length)];
                let a, b, answer;

                switch(op) {
                    case '+':
                        a = Math.floor(Math.random() * 50) + 1;
                        b = Math.floor(Math.random() * 50) + 1;
                        answer = a + b;
                        break;
                    case '-':
                        a = Math.floor(Math.random() * 50) + 25;
                        b = Math.floor(Math.random() * 25) + 1;
                        answer = a - b;
                        break;
                    case '×':
                        a = Math.floor(Math.random() * 12) + 1;
                        b = Math.floor(Math.random() * 12) + 1;
                        answer = a * b;
                        break;
                    case '÷':
                        answer = Math.floor(Math.random() * 12) + 1;
                        b = Math.floor(Math.random() * 12) + 1;
                        a = answer * b;
                        break;
                }

                return { question: `${a} ${op} ${b}`, answer: answer };
            }

            function showProblem() {
                const problem = generateProblem();
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 p-6 animate-fade-in">
                        <div class="max-w-4xl mx-auto">
                            <div class="flex justify-between items-center mb-8">
                                <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                    <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                    </svg>
                                    <span class="font-medium">Back Home</span>
                                </button>

                                <div class="text-center">
                                    <div class="text-4xl mb-2 animate-bounce-gentle">🥷</div>
                                    <h1 class="text-3xl font-bold gradient-text">Number Ninja</h1>
                                    <p class="text-white/60">Math Challenge</p>
                                </div>

                                <div class="text-white/80">Score: ${score}</div>
                            </div>

                            <div class="glass rounded-3xl p-8 mb-8">
                                <div class="grid grid-cols-3 gap-6 mb-8">
                                    <div class="glass rounded-2xl p-4 text-center">
                                        <div class="text-2xl font-bold text-white">${score}</div>
                                        <div class="text-white/60 text-sm">Score</div>
                                    </div>
                                    <div class="glass rounded-2xl p-4 text-center">
                                        <div class="text-2xl font-bold text-emerald-400" id="ninja-timer">${Math.floor(timeLeft/60)}:${(timeLeft%60).toString().padStart(2,'0')}</div>
                                        <div class="text-white/60 text-sm">Time Left</div>
                                    </div>
                                    <div class="glass rounded-2xl p-4 text-center">
                                        <div class="text-2xl font-bold text-purple-400">${currentProblem + 1}</div>
                                        <div class="text-white/60 text-sm">Problem</div>
                                    </div>
                                </div>

                                <div class="text-center mb-8">
                                    <h2 class="text-4xl font-bold text-white mb-8">${problem.question} = ?</h2>

                                    <input type="number" id="math-answer" placeholder="Your answer..." class="w-full max-w-xs mx-auto bg-white/10 border border-white/20 rounded-2xl px-6 py-4 text-white text-center text-2xl placeholder-white/50 focus:outline-none focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 mb-6">

                                    <div class="space-y-4">
                                        <button onclick="checkMathAnswer(${problem.answer})" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-12 rounded-2xl text-xl shadow-2xl transform hover:scale-105 transition-all duration-300">
                                            Submit Answer
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                setTimeout(() => document.getElementById('math-answer').focus(), 100);

                document.getElementById('math-answer').addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') checkMathAnswer(problem.answer);
                });
            }

            // Timer
            const timer = setInterval(() => {
                timeLeft--;
                const mins = Math.floor(timeLeft / 60);
                const secs = timeLeft % 60;
                if (document.getElementById('ninja-timer')) {
                    document.getElementById('ninja-timer').textContent = `${mins}:${secs.toString().padStart(2, '0')}`;
                }

                if (timeLeft <= 0) {
                    clearInterval(timer);
                    showNinjaResults();
                }
            }, 1000);

            window.checkMathAnswer = function(correctAnswer) {
                const userAnswer = parseInt(document.getElementById('math-answer').value);
                if (userAnswer === correctAnswer) {
                    score++;
                    showCelebrationEffect();
                }
                currentProblem++;
                if (timeLeft > 3) {
                    setTimeout(showProblem, 1000);
                } else {
                    clearInterval(timer);
                    showNinjaResults();
                }
            };

            function showNinjaResults() {
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 flex items-center justify-center p-8">
                        <div class="max-w-2xl mx-auto text-center animate-scale-in">
                            <div class="glass rounded-3xl p-12">
                                <div class="text-8xl mb-6">🥷</div>
                                <h2 class="text-4xl font-bold text-white mb-4 gradient-text">Math Ninja!</h2>
                                <p class="text-xl text-white/80 mb-4">You solved <span class="text-emerald-400 font-bold">${score}</span> problems correctly!</p>
                                <p class="text-white/60 mb-8">You've earned <span class="text-yellow-400 font-semibold">+${Math.max(15, score * 3)} coins</span>!</p>

                                <div class="space-y-4">
                                    <button onclick="startSpecificActivity('number-ninja')" class="w-full bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🥷 More Math
                                    </button>
                                    <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🎯 Try Another Activity
                                    </button>
                                    <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                        🏠 Back to Home
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            showProblem();
        }

        // Rhyme Time Game
        function startRhymeTimeGame() {
            const rhymeWords = [
                { word: "CAT", rhymes: ["BAT", "HAT", "MAT", "RAT", "SAT", "FAT"], nonRhymes: ["DOG", "SUN", "CAR"] },
                { word: "TREE", rhymes: ["BEE", "SEE", "FREE", "KEY", "TEA", "ME"], nonRhymes: ["BIRD", "ROCK", "FISH"] },
                { word: "LIGHT", rhymes: ["NIGHT", "BRIGHT", "SIGHT", "FIGHT", "RIGHT", "MIGHT"], nonRhymes: ["DARK", "MOON", "STAR"] },
                { word: "RAIN", rhymes: ["PAIN", "TRAIN", "BRAIN", "CHAIN", "MAIN", "GAIN"], nonRhymes: ["SNOW", "WIND", "CLOUD"] },
                { word: "BOOK", rhymes: ["LOOK", "TOOK", "COOK", "HOOK", "SHOOK", "BROOK"], nonRhymes: ["READ", "PAGE", "WORD"] }
            ];

            let currentWord = 0, score = 0, timeLeft = selectedTime * 60;

            function showRhymeChallenge() {
                const challenge = rhymeWords[currentWord];
                const allOptions = [...challenge.rhymes.slice(0, 3), ...challenge.nonRhymes];
                const shuffled = allOptions.sort(() => Math.random() - 0.5);

                document.querySelector('main').innerHTML = `
                    <div class="flex-1 p-6 animate-fade-in">
                        <div class="max-w-4xl mx-auto">
                            <div class="flex justify-between items-center mb-8">
                                <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                    <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                    </svg>
                                    <span class="font-medium">Back Home</span>
                                </button>

                                <div class="text-center">
                                    <div class="text-4xl mb-2 animate-bounce-gentle">🎵</div>
                                    <h1 class="text-3xl font-bold gradient-text">Rhyme Time</h1>
                                    <p class="text-white/60">Word ${currentWord + 1} of ${rhymeWords.length}</p>
                                </div>

                                <div class="text-white/80">Score: ${score}</div>
                            </div>

                            <div class="glass rounded-3xl p-8 mb-8">
                                <div class="text-center mb-8">
                                    <h2 class="text-2xl font-bold text-white mb-4">Which words rhyme with:</h2>
                                    <div class="text-6xl font-bold text-white mb-8">${challenge.word}</div>
                                    <p class="text-lg text-white/70 mb-8">Click all the words that rhyme!</p>

                                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4 max-w-2xl mx-auto mb-8">
                                        ${shuffled.map(word => `
                                            <button onclick="selectRhyme('${word}', ${challenge.rhymes.includes(word)})" class="rhyme-option glass rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 transform hover:scale-105 group" data-word="${word}">
                                                <div class="text-lg text-white font-medium">${word}</div>
                                            </button>
                                        `).join('')}
                                    </div>

                                    <button onclick="checkRhymes()" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-12 rounded-2xl text-xl shadow-2xl transform hover:scale-105 transition-all duration-300">
                                        Check Answers
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            let selectedWords = [];

            window.selectRhyme = function(word, isCorrect) {
                const button = document.querySelector(`[data-word="${word}"]`);
                if (selectedWords.includes(word)) {
                    selectedWords = selectedWords.filter(w => w !== word);
                    button.classList.remove('bg-blue-500/50');
                } else {
                    selectedWords.push(word);
                    button.classList.add('bg-blue-500/50');
                }
            };

            window.checkRhymes = function() {
                const challenge = rhymeWords[currentWord];
                const correctRhymes = challenge.rhymes.slice(0, 3);
                const correctSelections = selectedWords.filter(word => correctRhymes.includes(word));
                const incorrectSelections = selectedWords.filter(word => !correctRhymes.includes(word));

                if (correctSelections.length === correctRhymes.length && incorrectSelections.length === 0) {
                    score++;
                    showCelebrationEffect();
                }

                selectedWords = [];
                currentWord++;

                if (currentWord < rhymeWords.length) {
                    setTimeout(showRhymeChallenge, 1500);
                } else {
                    showRhymeResults();
                }
            };

            function showRhymeResults() {
                document.querySelector('main').innerHTML = `
                    <div class="flex-1 flex items-center justify-center p-8">
                        <div class="max-w-2xl mx-auto text-center animate-scale-in">
                            <div class="glass rounded-3xl p-12">
                                <div class="text-8xl mb-6">🎵</div>
                                <h2 class="text-4xl font-bold text-white mb-4 gradient-text">Rhyme Master!</h2>
                                <p class="text-xl text-white/80 mb-4">You got <span class="text-emerald-400 font-bold">${score}</span> out of ${rhymeWords.length} perfect!</p>
                                <p class="text-white/60 mb-8">You've earned <span class="text-yellow-400 font-semibold">+${Math.max(10, score * 4)} coins</span>!</p>

                                <div class="space-y-4">
                                    <button onclick="startSpecificActivity('rhyme-time')" class="w-full bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🎵 More Rhymes
                                    </button>
                                    <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                        🎯 Try Another Activity
                                    </button>
                                    <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                        🏠 Back to Home
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            showRhymeChallenge();
        }
    </script>
</body>
</html>
