'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import confetti from 'canvas-confetti';
import { Activity, PatternTapState } from '@/types';

interface PatternTapProps {
  activity: Activity;
  onComplete: (coinsEarned: number) => void;
  timeSlot: number;
}

const colors = [
  { id: 0, name: 'Red', bg: 'bg-red-500', hover: 'hover:bg-red-600' },
  { id: 1, name: 'Blue', bg: 'bg-blue-500', hover: 'hover:bg-blue-600' },
  { id: 2, name: 'Green', bg: 'bg-green-500', hover: 'hover:bg-green-600' },
  { id: 3, name: 'Yellow', bg: 'bg-yellow-500', hover: 'hover:bg-yellow-600' },
];

export default function PatternTap({ activity, onComplete, timeSlot }: PatternTapProps) {
  const [gameState, setGameState] = useState<PatternTapState>({
    score: 0,
    timeRemaining: timeSlot * 60, // Convert minutes to seconds
    isActive: false,
    isCompleted: false,
    sequence: [],
    userSequence: [],
    currentStep: 0,
    showingSequence: false,
  });

  const [message, setMessage] = useState('Ready to start?');
  const [activeColor, setActiveColor] = useState<number | null>(null);
  const [particles, setParticles] = useState<Array<{id: number, x: number, y: number, color: string}>>([]);
  const [showSuccess, setShowSuccess] = useState(false);
  const [scoreAnimation, setScoreAnimation] = useState(false);

  // Generate a new sequence
  const generateSequence = useCallback((length: number) => {
    const sequence = [];
    for (let i = 0; i < length; i++) {
      sequence.push(Math.floor(Math.random() * colors.length));
    }
    return sequence;
  }, []);

  // Start a new round
  const startNewRound = useCallback(() => {
    const sequenceLength = Math.min(3 + Math.floor(gameState.score / 2), 8);
    const newSequence = generateSequence(sequenceLength);
    
    setGameState(prev => ({
      ...prev,
      sequence: newSequence,
      userSequence: [],
      currentStep: 0,
      showingSequence: true,
    }));
    
    setMessage('Watch the pattern...');
    
    // Show sequence
    let index = 0;
    const showSequence = () => {
      if (index < newSequence.length) {
        setActiveColor(newSequence[index]);
        setTimeout(() => {
          setActiveColor(null);
          setTimeout(() => {
            index++;
            showSequence();
          }, 200);
        }, 600);
      } else {
        setGameState(prev => ({ ...prev, showingSequence: false }));
        setMessage('Now repeat the pattern!');
      }
    };
    
    setTimeout(showSequence, 500);
  }, [gameState.score, generateSequence]);

  // Create particle explosion effect
  const createParticleExplosion = (colorId: number, isCorrect: boolean) => {
    const color = colors[colorId];
    const particleColor = isCorrect ? color.bg.replace('bg-', '') : 'red-500';

    const newParticles = Array.from({ length: 8 }, (_, i) => ({
      id: Date.now() + i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      color: particleColor
    }));

    setParticles(newParticles);

    // Clear particles after animation
    setTimeout(() => setParticles([]), 1000);

    if (isCorrect) {
      // Confetti for correct answer
      confetti({
        particleCount: 20,
        spread: 60,
        origin: { y: 0.7 },
        colors: ['#ef4444', '#3b82f6', '#10b981', '#eab308']
      });
    }
  };

  // Handle color button click
  const handleColorClick = (colorId: number) => {
    if (gameState.showingSequence || gameState.isCompleted) return;

    const newUserSequence = [...gameState.userSequence, colorId];
    const currentIndex = gameState.userSequence.length;
    const isCorrect = colorId === gameState.sequence[currentIndex];

    // Create particle explosion
    createParticleExplosion(colorId, isCorrect);

    if (isCorrect) {
      // Correct!
      setGameState(prev => ({ ...prev, userSequence: newUserSequence }));

      if (newUserSequence.length === gameState.sequence.length) {
        // Round completed!
        const newScore = gameState.score + 1;
        setGameState(prev => ({ ...prev, score: newScore }));
        setMessage(`Great! Score: ${newScore}`);
        setScoreAnimation(true);
        setShowSuccess(true);

        // Big celebration for round completion
        confetti({
          particleCount: 50,
          spread: 70,
          origin: { y: 0.6 },
          colors: ['#ef4444', '#3b82f6', '#10b981', '#eab308']
        });

        setTimeout(() => {
          setScoreAnimation(false);
          setShowSuccess(false);
          if (gameState.timeRemaining > 5) {
            startNewRound();
          } else {
            endGame();
          }
        }, 1500);
      }
    } else {
      // Wrong!
      setMessage('Oops! Try again...');
      setTimeout(() => {
        startNewRound();
      }, 1500);
    }
  };

  // Start the game
  const startGame = () => {
    setGameState(prev => ({
      ...prev,
      isActive: true,
      score: 0,
      timeRemaining: timeSlot * 60,
    }));
    startNewRound();
  };

  // End the game
  const endGame = () => {
    setGameState(prev => ({ ...prev, isActive: false, isCompleted: true }));
    setMessage(`Game Over! Final Score: ${gameState.score}`);
    
    // Calculate coins based on performance
    const coinsEarned = Math.max(5, gameState.score * 2);
    setTimeout(() => onComplete(coinsEarned), 2000);
  };

  // Timer effect
  useEffect(() => {
    if (gameState.isActive && gameState.timeRemaining > 0) {
      const timer = setTimeout(() => {
        setGameState(prev => ({ ...prev, timeRemaining: prev.timeRemaining - 1 }));
      }, 1000);
      return () => clearTimeout(timer);
    } else if (gameState.isActive && gameState.timeRemaining <= 0) {
      endGame();
    }
  }, [gameState.isActive, gameState.timeRemaining]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="text-center relative">
      {/* Floating Particles */}
      <div className="absolute inset-0 pointer-events-none">
        <AnimatePresence>
          {particles.map((particle) => (
            <motion.div
              key={particle.id}
              className={`absolute w-3 h-3 bg-${particle.color} rounded-full`}
              style={{ left: `${particle.x}%`, top: `${particle.y}%` }}
              initial={{ scale: 0, opacity: 1 }}
              animate={{
                scale: [0, 1, 0],
                y: [-20, -60, -100],
                x: [0, Math.random() * 40 - 20, Math.random() * 80 - 40],
                opacity: [1, 0.8, 0]
              }}
              exit={{ opacity: 0 }}
              transition={{ duration: 1, ease: "easeOut" }}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* Success Animation */}
      <AnimatePresence>
        {showSuccess && (
          <motion.div
            className="absolute inset-0 flex items-center justify-center pointer-events-none z-10"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
          >
            <div className="text-6xl">🎉</div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Game Stats */}
      <div className="flex justify-between items-center mb-6 text-white">
        <motion.div
          className="glass px-4 py-2 rounded-lg"
          animate={scoreAnimation ? { scale: [1, 1.2, 1] } : {}}
          transition={{ duration: 0.3 }}
        >
          Score: <span className="font-bold text-yellow-400">{gameState.score}</span>
        </motion.div>
        <motion.div
          className="glass px-4 py-2 rounded-lg"
          animate={{
            color: gameState.timeRemaining < 10 ? ["#ffffff", "#ef4444", "#ffffff"] : "#ffffff"
          }}
          transition={{ duration: 0.5, repeat: gameState.timeRemaining < 10 ? Infinity : 0 }}
        >
          Time: {formatTime(gameState.timeRemaining)}
        </motion.div>
      </div>

      {/* Message */}
      <motion.div
        className="text-white text-lg mb-6 h-8"
        key={message}
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {message}
      </motion.div>

      {/* Color Buttons */}
      <div className="grid grid-cols-2 gap-4 mb-6 max-w-xs mx-auto relative">
        {colors.map((color, index) => (
          <motion.button
            key={color.id}
            onClick={() => handleColorClick(color.id)}
            disabled={gameState.showingSequence || gameState.isCompleted}
            className={`
              w-20 h-20 rounded-full transition-all duration-200 transform glass
              ${color.bg} ${color.hover}
              ${activeColor === color.id ? 'scale-110 ring-4 ring-white shadow-2xl' : ''}
              ${gameState.showingSequence ? 'cursor-not-allowed opacity-70' : 'hover:scale-105 hover:shadow-lg'}
              disabled:cursor-not-allowed relative overflow-hidden
            `}
            initial={{ scale: 0, rotate: -180 }}
            animate={{
              scale: 1,
              rotate: 0,
              boxShadow: activeColor === color.id ?
                "0 0 30px rgba(255, 255, 255, 0.8)" :
                "0 4px 15px rgba(0, 0, 0, 0.2)"
            }}
            transition={{
              delay: index * 0.1,
              type: "spring",
              boxShadow: { duration: 0.3 }
            }}
            whileHover={{
              scale: gameState.showingSequence ? 1 : 1.1,
              y: gameState.showingSequence ? 0 : -5
            }}
            whileTap={{ scale: 0.9 }}
          >
            <span className="sr-only">{color.name}</span>
            {/* Ripple effect overlay */}
            <motion.div
              className="absolute inset-0 bg-white rounded-full"
              initial={{ scale: 0, opacity: 0 }}
              animate={activeColor === color.id ? {
                scale: [0, 1.5],
                opacity: [0.3, 0]
              } : {}}
              transition={{ duration: 0.6 }}
            />
          </motion.button>
        ))}
      </div>

      {/* Start/Status */}
      <AnimatePresence>
        {!gameState.isActive && !gameState.isCompleted && (
          <motion.button
            onClick={startGame}
            className="btn-magical text-white font-bold py-3 px-8 rounded-lg"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            Start Game
          </motion.button>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {gameState.isCompleted && (
          <motion.div
            className="text-white"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
          >
            <motion.p
              className="text-xl font-bold mb-2"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 0.5, repeat: 2 }}
            >
              Well done! 🎉
            </motion.p>
            <p className="text-white/80">Completing activity...</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Instructions */}
      <AnimatePresence>
        {!gameState.isActive && (
          <motion.div
            className="mt-6 text-white/60 text-sm glass p-4 rounded-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ delay: 0.5 }}
          >
            <p>Watch the pattern, then repeat it by tapping the colors in order.</p>
            <p>Each correct sequence increases your score!</p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
