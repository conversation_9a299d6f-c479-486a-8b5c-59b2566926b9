'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import confetti from 'canvas-confetti';
import { Activity } from '@/types';
import { Heart, Plus, Check, Sparkles } from 'lucide-react';

interface GratitudeDropProps {
  activity: Activity;
  onComplete: (coinsEarned: number) => void;
  timeSlot: number;
}

const gratitudePrompts = [
  "What made you smile today?",
  "Who are you thankful for right now?",
  "What's something beautiful you noticed recently?",
  "What skill or ability are you grateful to have?",
  "What's a small pleasure that brightened your day?",
  "What challenge helped you grow?",
  "What's something in nature you appreciate?",
  "What memory brings you joy?",
  "What opportunity are you grateful for?",
  "What comfort do you have that others might not?"
];

export default function GratitudeDrop({ activity, onComplete, timeSlot }: GratitudeDropProps) {
  const [gratitudeItems, setGratitudeItems] = useState<string[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [currentPrompt, setCurrentPrompt] = useState(
    gratitudePrompts[Math.floor(Math.random() * gratitudePrompts.length)]
  );
  const [isCompleted, setIsCompleted] = useState(false);
  const [heartParticles, setHeartParticles] = useState<Array<{id: number, x: number, y: number, delay: number}>>([]);
  const [showCelebration, setShowCelebration] = useState(false);

  const targetCount = 3; // Aim for 3 gratitude items

  // Initialize floating heart particles
  useEffect(() => {
    const particles = Array.from({ length: 12 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 6
    }));
    setHeartParticles(particles);
  }, []);

  const addGratitudeItem = () => {
    if (currentInput.trim()) {
      setGratitudeItems(prev => [...prev, currentInput.trim()]);
      setCurrentInput('');

      // Celebration for each item added
      confetti({
        particleCount: 20,
        spread: 50,
        origin: { y: 0.7 },
        colors: ['#ec4899', '#f97316', '#eab308', '#10b981']
      });

      // Get a new prompt for the next item
      if (gratitudeItems.length + 1 < targetCount) {
        const availablePrompts = gratitudePrompts.filter(p => p !== currentPrompt);
        setCurrentPrompt(availablePrompts[Math.floor(Math.random() * availablePrompts.length)]);
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      addGratitudeItem();
    }
  };

  const completeActivity = () => {
    setIsCompleted(true);
    setShowCelebration(true);

    // Big celebration
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 },
      colors: ['#ec4899', '#f97316', '#eab308', '#10b981', '#8b5cf6']
    });

    const coinsEarned = Math.max(6, gratitudeItems.length * 3);
    setTimeout(() => onComplete(coinsEarned), 2000);
  };

  const removeItem = (index: number) => {
    setGratitudeItems(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="max-w-2xl mx-auto relative">
      {/* Floating Heart Particles */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {heartParticles.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute"
            style={{ left: `${particle.x}%`, top: `${particle.y}%` }}
            animate={{
              y: [-20, -40, -20],
              rotate: [0, 360],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              delay: particle.delay,
              ease: "easeInOut"
            }}
          >
            <Heart className="w-4 h-4 text-pink-300/30" />
          </motion.div>
        ))}
      </div>

      {/* Celebration Animation */}
      <AnimatePresence>
        {showCelebration && (
          <motion.div
            className="absolute inset-0 flex items-center justify-center pointer-events-none z-20"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
          >
            <motion.div
              className="text-6xl"
              animate={{ rotate: [0, 360], scale: [1, 1.2, 1] }}
              transition={{ duration: 1 }}
            >
              ✨💖✨
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Header */}
      <motion.div
        className="flex items-center justify-center gap-3 mb-6 relative z-10"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <motion.div
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <Heart className="w-6 h-6 text-pink-400" />
        </motion.div>
        <h3 className="text-xl font-bold text-white">Gratitude Drop</h3>
      </motion.div>

      {/* Progress */}
      <motion.div
        className="mb-6 relative z-10"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.2 }}
      >
        <div className="flex items-center justify-between mb-2">
          <span className="text-white/80 text-sm">Progress</span>
          <motion.span
            className="text-white/80 text-sm"
            key={gratitudeItems.length}
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 0.3 }}
          >
            {gratitudeItems.length} / {targetCount}
          </motion.span>
        </div>
        <div className="w-full h-2 bg-white/20 rounded-full glass">
          <motion.div
            className="h-full bg-gradient-to-r from-pink-500 to-rose-500 rounded-full"
            animate={{
              width: `${(gratitudeItems.length / targetCount) * 100}%`,
              boxShadow: [
                "0 0 10px rgba(236, 72, 153, 0.5)",
                "0 0 20px rgba(236, 72, 153, 0.8)",
                "0 0 10px rgba(236, 72, 153, 0.5)"
              ]
            }}
            transition={{
              width: { duration: 0.5 },
              boxShadow: { duration: 1.5, repeat: Infinity }
            }}
          />
        </div>
      </motion.div>

      {/* Current Prompt */}
      <AnimatePresence>
        {!isCompleted && gratitudeItems.length < targetCount && (
          <motion.div
            className="glass rounded-2xl p-6 mb-6 relative z-10"
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ duration: 0.5 }}
          >
            <motion.h4
              className="text-lg font-semibold text-white mb-4 text-center"
              key={currentPrompt}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <motion.span
                animate={{
                  textShadow: [
                    "0 0 10px rgba(236, 72, 153, 0.3)",
                    "0 0 20px rgba(236, 72, 153, 0.6)",
                    "0 0 10px rgba(236, 72, 153, 0.3)"
                  ]
                }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                {currentPrompt}
              </motion.span>
            </motion.h4>

            <div className="space-y-4">
              <motion.textarea
                value={currentInput}
                onChange={(e) => setCurrentInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your thoughts here..."
                className="w-full h-24 glass border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 resize-none focus:outline-none focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 transition-all"
                maxLength={200}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
              />

              <div className="flex items-center justify-between">
                <motion.span
                  className="text-white/50 text-sm"
                  animate={{
                    color: currentInput.length > 180 ? "#ef4444" : "rgba(255, 255, 255, 0.5)"
                  }}
                >
                  {currentInput.length}/200 characters
                </motion.span>
                <motion.button
                  onClick={addGratitudeItem}
                  disabled={!currentInput.trim()}
                  className="flex items-center gap-2 btn-magical text-white font-medium py-2 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={currentInput.trim() ? { scale: 1.05, y: -2 } : {}}
                  whileTap={currentInput.trim() ? { scale: 0.95 } : {}}
                >
                  <Plus className="w-4 h-4" />
                  Add
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Gratitude List */}
      <AnimatePresence>
        {gratitudeItems.length > 0 && (
          <motion.div
            className="mb-6 relative z-10"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <motion.h4
              className="text-lg font-semibold text-white mb-4 flex items-center gap-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <Heart className="w-5 h-5 text-pink-400" />
              </motion.div>
              Your Gratitude Jar
            </motion.h4>

            <div className="space-y-3">
              <AnimatePresence>
                {gratitudeItems.map((item, index) => (
                  <motion.div
                    key={index}
                    className="glass rounded-lg p-4 flex items-start gap-3 hover-lift"
                    initial={{ opacity: 0, x: -50, scale: 0.8 }}
                    animate={{ opacity: 1, x: 0, scale: 1 }}
                    exit={{ opacity: 0, x: 50, scale: 0.8 }}
                    transition={{
                      delay: index * 0.1,
                      type: "spring",
                      stiffness: 300
                    }}
                    whileHover={{ scale: 1.02, y: -2 }}
                  >
                    <motion.div
                      className="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center text-white text-sm font-bold"
                      animate={{
                        boxShadow: [
                          "0 0 10px rgba(236, 72, 153, 0.5)",
                          "0 0 20px rgba(236, 72, 153, 0.8)",
                          "0 0 10px rgba(236, 72, 153, 0.5)"
                        ]
                      }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      {index + 1}
                    </motion.div>
                    <p className="text-white/90 flex-1">{item}</p>
                    <motion.button
                      onClick={() => removeItem(index)}
                      className="text-white/40 hover:text-white/80 transition-colors"
                      whileHover={{ scale: 1.2, rotate: 90 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      ×
                    </motion.button>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Completion */}
      <AnimatePresence>
        {gratitudeItems.length >= targetCount && !isCompleted && (
          <motion.div
            className="text-center relative z-10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="glass rounded-2xl p-6 mb-6">
              <motion.div
                className="text-4xl mb-4"
                animate={{
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.2, 1]
                }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                ✨
              </motion.div>
              <motion.h4
                className="text-xl font-bold text-white mb-2"
                animate={{
                  textShadow: [
                    "0 0 10px rgba(236, 72, 153, 0.5)",
                    "0 0 20px rgba(236, 72, 153, 0.8)",
                    "0 0 10px rgba(236, 72, 153, 0.5)"
                  ]
                }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                Beautiful!
              </motion.h4>
              <motion.p
                className="text-white/80 mb-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                You've captured <motion.span
                  className="font-bold text-pink-300"
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 1, repeat: Infinity }}
                >
                  {gratitudeItems.length}
                </motion.span> moments of gratitude.
                Taking time to appreciate the good things in life can boost happiness and well-being.
              </motion.p>

              <motion.button
                onClick={completeActivity}
                className="btn-magical text-white font-bold py-3 px-8 rounded-lg"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                animate={{
                  boxShadow: [
                    "0 5px 15px rgba(236, 72, 153, 0.3)",
                    "0 10px 25px rgba(236, 72, 153, 0.5)",
                    "0 5px 15px rgba(236, 72, 153, 0.3)"
                  ]
                }}
                transition={{ boxShadow: { duration: 2, repeat: Infinity } }}
              >
                Complete Activity
              </motion.button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {isCompleted && (
          <motion.div
            className="text-center relative z-10"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.5 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <motion.div
              className="text-6xl mb-4"
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              💖
            </motion.div>
            <motion.h4
              className="text-2xl font-bold text-white mb-2"
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              Gratitude Shared!
            </motion.h4>
            <motion.p
              className="text-white/80"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              Thank you for taking time to appreciate life's gifts.
            </motion.p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Instructions */}
      <AnimatePresence>
        {gratitudeItems.length === 0 && !isCompleted && (
          <motion.div
            className="text-center text-white/60 text-sm glass p-4 rounded-lg relative z-10"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ delay: 0.8 }}
          >
            <motion.p
              animate={{ opacity: [0.6, 1, 0.6] }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              Take a moment to reflect on the good things in your life.
            </motion.p>
            <motion.p
              animate={{ opacity: [0.6, 1, 0.6] }}
              transition={{ duration: 3, repeat: Infinity, delay: 1.5 }}
            >
              Research shows gratitude practice can improve mood and well-being!
            </motion.p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
